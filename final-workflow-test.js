#!/usr/bin/env node

// Final comprehensive workflow test for TurdParty React UI
// Tests all workflow pages and functionality

const { execSync } = require('child_process');

// Colors for output
const colors = {
    red: '\x1b[31m',
    green: '\x1b[32m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    purple: '\x1b[35m',
    cyan: '\x1b[36m',
    reset: '\x1b[0m'
};

function printColored(color, text) {
    console.log(`${colors[color]}${text}${colors.reset}`);
}

async function runWorkflowTest() {
    printColored('purple', '🎯 TurdParty React UI Workflow Test');
    console.log('');

    const baseUrl = 'http://localhost:3000';
    let passedTests = 0;
    let totalTests = 0;

    // Test 1: Main application loading
    totalTests++;
    printColored('yellow', '📋 Test 1: Main Application Loading');
    try {
        const response = execSync(`curl -f -s "${baseUrl}/"`, { encoding: 'utf8' });
        if (response.includes('TurdParty') && response.includes('<!DOCTYPE html>')) {
            printColored('green', '✅ Main application loads: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Main application content: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Main application loading: FAIL');
    }

    // Test 2: React bundle verification
    totalTests++;
    printColored('yellow', '📋 Test 2: React Bundle Verification');
    try {
        const response = execSync(`curl -f -s "${baseUrl}/"`, { encoding: 'utf8' });
        const hasJS = response.includes('static/js/main') || response.includes('.js');
        const hasCSS = response.includes('static/css/main') || response.includes('.css');
        
        if (hasJS && hasCSS) {
            printColored('green', '✅ React bundle assets: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ React bundle assets: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ React bundle verification: FAIL');
    }

    // Test 3: Workflow page routing
    totalTests++;
    printColored('yellow', '📋 Test 3: Workflow Page Routing');
    const workflowPages = [
        '/file_upload',
        '/file_selection', 
        '/vm_injection',
        '/vm_status'
    ];
    
    let routingPassed = 0;
    for (const page of workflowPages) {
        try {
            const response = execSync(`curl -f -s "${baseUrl}${page}"`, { encoding: 'utf8' });
            if (response.includes('TurdParty') && response.includes('<!DOCTYPE html>')) {
                routingPassed++;
                printColored('cyan', `   ✅ ${page}: accessible`);
            } else {
                printColored('cyan', `   ❌ ${page}: content issue`);
            }
        } catch (error) {
            printColored('cyan', `   ❌ ${page}: not accessible`);
        }
    }
    
    if (routingPassed === workflowPages.length) {
        printColored('green', '✅ Workflow page routing: PASS');
        passedTests++;
    } else {
        printColored('red', `❌ Workflow page routing: FAIL (${routingPassed}/${workflowPages.length})`);
    }

    // Test 4: API proxy configuration
    totalTests++;
    printColored('yellow', '📋 Test 4: API Proxy Configuration');
    try {
        // Test API proxy - should get some response (even 404/502)
        const response = execSync(`curl -s "${baseUrl}/api/v1/health"`, { encoding: 'utf8', stdio: 'pipe' });
        printColored('green', '✅ API proxy configured: PASS');
        passedTests++;
    } catch (error) {
        printColored('red', '❌ API proxy configuration: FAIL');
    }

    // Test 5: Health endpoint functionality
    totalTests++;
    printColored('yellow', '📋 Test 5: Health Endpoint Functionality');
    try {
        const response = execSync(`curl -f -s "${baseUrl}/health"`, { encoding: 'utf8' });
        if (response.trim() === 'healthy') {
            printColored('green', '✅ Health endpoint: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Health endpoint response: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Health endpoint: FAIL');
    }

    // Test 6: Static asset serving
    totalTests++;
    printColored('yellow', '📋 Test 6: Static Asset Serving');
    try {
        // Test favicon or other static assets
        const response = execSync(`curl -s -o /dev/null -w "%{http_code}" "${baseUrl}/favicon.ico"`, { encoding: 'utf8' });
        if (response === '200' || response === '404') {
            printColored('green', '✅ Static asset serving: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Static asset serving: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Static asset serving: FAIL');
    }

    // Test 7: Container health and performance
    totalTests++;
    printColored('yellow', '📋 Test 7: Container Health and Performance');
    try {
        const containerStatus = execSync(`docker ps --filter "name=turdpartycollab_frontend" --format "{{.Status}}"`, { encoding: 'utf8' });
        const memoryUsage = execSync(`docker stats --no-stream --format "{{.MemUsage}}" turdpartycollab_frontend`, { encoding: 'utf8' });
        
        if (containerStatus.includes('healthy') || containerStatus.includes('Up')) {
            printColored('green', '✅ Container health: PASS');
            printColored('cyan', `   Memory Usage: ${memoryUsage.trim()}`);
            passedTests++;
        } else {
            printColored('red', '❌ Container health: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Container health check: FAIL');
    }

    // Test 8: Response time performance
    totalTests++;
    printColored('yellow', '📋 Test 8: Response Time Performance');
    try {
        const responseTime = execSync(`curl -s -o /dev/null -w "%{time_total}" "${baseUrl}/"`, { encoding: 'utf8' });
        const timeMs = Math.round(parseFloat(responseTime) * 1000);
        
        if (timeMs < 2000) {
            printColored('green', `✅ Response time: PASS (${timeMs}ms)`);
            passedTests++;
        } else {
            printColored('red', `❌ Response time: FAIL (${timeMs}ms - too slow)`);
        }
    } catch (error) {
        printColored('red', '❌ Response time test: FAIL');
    }

    // Test 9: Nginx configuration
    totalTests++;
    printColored('yellow', '📋 Test 9: Nginx Configuration');
    try {
        // Test nginx headers and configuration
        const headers = execSync(`curl -s -I "${baseUrl}/"`, { encoding: 'utf8' });
        if (headers.includes('nginx') && headers.includes('200 OK')) {
            printColored('green', '✅ Nginx configuration: PASS');
            passedTests++;
        } else {
            printColored('red', '❌ Nginx configuration: FAIL');
        }
    } catch (error) {
        printColored('red', '❌ Nginx configuration: FAIL');
    }

    // Test 10: Complete workflow simulation
    totalTests++;
    printColored('yellow', '📋 Test 10: Complete Workflow Simulation');
    let workflowSteps = 0;
    
    // Step 1: Access main page
    try {
        execSync(`curl -f -s "${baseUrl}/" > /dev/null`);
        workflowSteps++;
    } catch (e) {}
    
    // Step 2: Access file upload
    try {
        execSync(`curl -f -s "${baseUrl}/file_upload" > /dev/null`);
        workflowSteps++;
    } catch (e) {}
    
    // Step 3: Access file selection
    try {
        execSync(`curl -f -s "${baseUrl}/file_selection" > /dev/null`);
        workflowSteps++;
    } catch (e) {}
    
    // Step 4: Access VM injection
    try {
        execSync(`curl -f -s "${baseUrl}/vm_injection" > /dev/null`);
        workflowSteps++;
    } catch (e) {}
    
    // Step 5: Access VM status
    try {
        execSync(`curl -f -s "${baseUrl}/vm_status" > /dev/null`);
        workflowSteps++;
    } catch (e) {}
    
    if (workflowSteps === 5) {
        printColored('green', '✅ Complete workflow simulation: PASS');
        passedTests++;
    } else {
        printColored('red', `❌ Complete workflow simulation: FAIL (${workflowSteps}/5 steps)`);
    }

    // Calculate final results
    const successRate = Math.round((passedTests / totalTests) * 100);
    
    console.log('');
    printColored('purple', '📊 Final Workflow Test Results:');
    printColored('blue', `   Total Tests: ${totalTests}`);
    printColored('green', `   Passed: ${passedTests}`);
    printColored('red', `   Failed: ${totalTests - passedTests}`);
    printColored('blue', `   Success Rate: ${successRate}%`);
    
    console.log('');
    if (successRate >= 90) {
        printColored('green', '🎉 EXCELLENT: React UI workflow is working perfectly!');
        printColored('green', '✅ TurdParty frontend is ready for production use');
    } else if (successRate >= 80) {
        printColored('yellow', '👍 GOOD: React UI workflow is working well');
        printColored('yellow', '⚠️  Minor issues detected but workflow is functional');
    } else if (successRate >= 70) {
        printColored('yellow', '⚠️  FAIR: React UI has some workflow issues');
        printColored('yellow', '🔧 Some components need attention');
    } else {
        printColored('red', '❌ POOR: Significant workflow issues detected');
        printColored('red', '🚨 Frontend requires immediate attention');
    }

    console.log('');
    printColored('blue', '🎯 TurdParty Workflow Ready:');
    printColored('cyan', '   1. File Upload → http://localhost:3000/file_upload');
    printColored('cyan', '   2. File Selection → http://localhost:3000/file_selection');
    printColored('cyan', '   3. Template Selection → http://localhost:3000/vm_injection');
    printColored('cyan', '   4. VM & Outcome → http://localhost:3000/vm_status');
    
    console.log('');
    printColored('blue', '🌐 Access Points:');
    printColored('cyan', '   Main UI: http://localhost:3000');
    printColored('cyan', '   Health: http://localhost:3000/health');
    printColored('cyan', '   Domain: http://frontend.turdparty.localhost (with DNS)');
    
    console.log('');
    
    return successRate >= 80;
}

// Run the workflow test
runWorkflowTest().then(success => {
    process.exit(success ? 0 : 1);
}).catch(error => {
    printColored('red', `❌ Workflow test execution failed: ${error.message}`);
    process.exit(1);
});
