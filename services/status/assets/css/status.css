/* TurdParty Status Page Styles */

:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --success-color: #27ae60;
    --warning-color: #f39c12;
    --danger-color: #e74c3c;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #2c3e50;
    --border-color: #bdc3c7;
    --text-color: #2c3e50;
    --bg-color: #ffffff;
    --card-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--light-color);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.status-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 300;
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 1.5rem;
}

.overall-status {
    display: inline-flex;
    align-items: center;
    background: rgba(255,255,255,0.1);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.8rem;
    animation: pulse 2s infinite;
}

.status-indicator.operational {
    background-color: var(--success-color);
}

.status-indicator.degraded {
    background-color: var(--warning-color);
}

.status-indicator.outage {
    background-color: var(--danger-color);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Section Styles */
section {
    background: var(--bg-color);
    margin: 2rem 0;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
}

section h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    font-weight: 400;
}

/* Architecture Section */
.mermaid-container {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    overflow-x: auto;
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.service-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.service-name {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--primary-color);
}

.service-status {
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.service-status.operational {
    background-color: var(--success-color);
    color: white;
}

.service-status.degraded {
    background-color: var(--warning-color);
    color: white;
}

.service-status.outage {
    background-color: var(--danger-color);
    color: white;
}

.service-details {
    color: #666;
    font-size: 0.9rem;
}

.service-port {
    font-family: 'Courier New', monospace;
    background: #f1f2f6;
    padding: 0.2rem 0.5rem;
    border-radius: 4px;
    margin-top: 0.5rem;
    display: inline-block;
}

.service-endpoint {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.3rem;
    word-break: break-all;
}

.service-metrics {
    margin-top: 1rem;
    padding-top: 0.8rem;
    border-top: 1px solid #eee;
}

.service-metrics .metric {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-metrics .metric:last-child {
    margin-bottom: 0;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    text-align: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 1rem;
    border-radius: 10px;
    transition: transform 0.3s ease;
}

.metric-card:hover {
    transform: scale(1.05);
}

.metric-card h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Activity Feed */
.activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.activity-icon.success {
    background-color: var(--success-color);
    color: white;
}

.activity-icon.warning {
    background-color: var(--warning-color);
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.activity-time {
    color: #666;
    font-size: 0.9rem;
}

/* Footer */
.status-footer {
    background: var(--primary-color);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.footer-links {
    display: flex;
    gap: 1.5rem;
}

.footer-links a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .footer-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .metric-value {
        font-size: 2rem;
    }
}

/* Mermaid Status Light Animations */
.mermaid-container .mermaid {
    transition: all 0.3s ease;
}

/* Status light pulse animation for operational services */
@keyframes statusPulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Status light blink animation for degraded services */
@keyframes statusBlink {
    0% { opacity: 1; }
    25% { opacity: 0.5; }
    50% { opacity: 1; }
    75% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Status light urgent blink for outage services */
@keyframes statusUrgent {
    0% { opacity: 1; }
    20% { opacity: 0.3; }
    40% { opacity: 1; }
    60% { opacity: 0.3; }
    80% { opacity: 1; }
    100% { opacity: 0.3; }
}

/* Apply animations to Mermaid diagram elements */
.mermaid .node rect,
.mermaid .node circle,
.mermaid .node polygon {
    transition: all 0.3s ease;
}

/* Enhanced service card animations */
.service-card {
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

/* Status indicator enhancements */
.service-status {
    position: relative;
    overflow: hidden;
}

.service-status.operational {
    animation: statusPulse 3s infinite ease-in-out;
}

.service-status.degraded {
    animation: statusBlink 2s infinite ease-in-out;
}

.service-status.outage {
    animation: statusUrgent 1s infinite ease-in-out;
}
