/* TurdParty Status Page Styles */

:root {
    --primary-color: #7c4dff;
    --secondary-color: #651fff;
    --success-color: #4caf50;
    --warning-color: #ff9800;
    --danger-color: #f44336;
    --info-color: #17a2b8;
    --light-color: #ecf0f1;
    --dark-color: #121212;
    --border-color: #333333;
    --text-color: #ffffff;
    --text-primary: #ffffff;
    --text-secondary: #b3b3b3;
    --text-muted: #666666;
    --bg-color: #121212;
    --card-bg: #1e1e1e;
    --card-shadow: 0 4px 20px rgba(0,0,0,0.3);
    --shadow: 0 4px 20px rgba(0,0,0,0.3);
    --hover-bg: #2a2a2a;
    --surface-color: #2d2d2d;
    --accent-color: #bb86fc;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-color);
    background-image:
        radial-gradient(circle at 20% 80%, rgba(124, 77, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(187, 134, 252, 0.1) 0%, transparent 50%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.status-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 3rem 0;
    text-align: center;
    margin-bottom: 2rem;
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 20px rgba(124, 77, 255, 0.2);
}

.header-content h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin-bottom: 1.5rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.overall-status {
    display: inline-flex;
    align-items: center;
    background: rgba(255,255,255,0.1);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    backdrop-filter: blur(10px);
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.8rem;
    animation: pulse 2s infinite;
}

.status-indicator.operational {
    background-color: var(--success-color);
}

.status-indicator.degraded {
    background-color: var(--warning-color);
}

.status-indicator.outage {
    background-color: var(--danger-color);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Section Styles */
section {
    background: var(--bg-color);
    margin: 2rem 0;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: var(--card-shadow);
}

section h2 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.8rem;
    font-weight: 400;
}

/* Architecture Section */
.mermaid-container {
    background: var(--surface-color);
    border-radius: 12px;
    padding: 1.5rem;
    overflow-x: auto;
    border: 1px solid var(--border-color);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Services Grid */
.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.service-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(124, 77, 255, 0.2);
    border-color: var(--primary-color);
}

.service-header {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1rem;
}

.service-icon {
    font-size: 2rem;
    flex-shrink: 0;
}

.service-info {
    flex: 1;
}

.service-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.service-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.85rem;
    font-weight: 500;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.service-status.operational .status-indicator {
    background: #28a745;
}

.service-status.degraded .status-indicator {
    background: #ffc107;
}

.service-status.outage .status-indicator {
    background: #dc3545;
}

.service-status.checking .status-indicator {
    background: #6c757d;
}

.service-status.operational .status-text {
    color: #28a745;
}

.service-status.degraded .status-text {
    color: #856404;
}

.service-status.outage .status-text {
    color: #dc3545;
}

.service-status.checking .status-text {
    color: #6c757d;
}

.service-details {
    margin-bottom: 1rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.25rem 0;
    font-size: 0.9rem;
}

.detail-label {
    color: var(--text-secondary);
    font-weight: 500;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 400;
}

.service-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 1rem;
    padding: 0.75rem;
    background: var(--hover-bg);
    border-radius: 6px;
}

.service-endpoint {
    font-family: 'Courier New', monospace;
    font-size: 0.8rem;
    color: var(--text-muted);
    background: var(--surface-color);
    padding: 0.5rem;
    border-radius: 4px;
    word-break: break-all;
    border-left: 3px solid var(--primary-color);
    border: 1px solid var(--border-color);
}

.service-metrics {
    margin-top: 1rem;
    padding-top: 0.8rem;
    border-top: 1px solid #eee;
}

.service-metrics .metric {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 0.3rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-metrics .metric:last-child {
    margin-bottom: 0;
}

/* Metrics Grid */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
}

.metric-card {
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    padding: 2rem 1rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    border: 1px solid rgba(124, 77, 255, 0.3);
    box-shadow: var(--shadow);
}

.metric-card:hover {
    transform: scale(1.05);
}

.metric-card h3 {
    font-size: 1rem;
    margin-bottom: 1rem;
    opacity: 0.9;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.metric-label {
    font-size: 0.9rem;
    opacity: 0.8;
}

/* Activity Feed */
.activity-feed {
    max-height: 400px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: background-color 0.3s ease;
}

.activity-item:hover {
    background-color: var(--hover-bg);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    font-size: 1.2rem;
}

.activity-icon.success {
    background-color: var(--success-color);
    color: white;
}

.activity-icon.warning {
    background-color: var(--warning-color);
    color: white;
}

.activity-content {
    flex: 1;
}

.activity-title {
    font-weight: 600;
    margin-bottom: 0.3rem;
}

.activity-time {
    color: #666;
    font-size: 0.9rem;
}

/* Tab Navigation */
.tab-navigation {
    margin: 2rem 0;
}

.tab-container {
    display: flex;
    background: var(--card-bg);
    border-radius: 12px;
    padding: 0.5rem;
    gap: 0.5rem;
    box-shadow: var(--shadow);
    max-width: 1200px;
    margin: 0 auto;
    border: 1px solid var(--border-color);
}

.tab-button {
    flex: 1;
    padding: 1rem 2rem;
    border: none;
    background: transparent;
    color: var(--text-secondary);
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.tab-button:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
}

.tab-button.active {
    background: var(--primary-color);
    color: white;
    box-shadow: 0 2px 8px rgba(74, 20, 140, 0.3);
}

/* Tab Content */
.tab-content-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

/* Mini Service Status Icons */
.mini-services-section {
    margin-bottom: 2rem;
}

.mini-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.mini-service-card {
    background: var(--card-bg);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 0.75rem;
    text-align: center;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
}

.mini-service-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(124, 77, 255, 0.2);
}

.mini-service-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.mini-service-name {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 0.25rem;
}

.mini-service-status {
    font-size: 0.7rem;
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-weight: 500;
}

.mini-service-status.operational {
    background: rgba(76, 175, 80, 0.2);
    color: #4caf50;
}

.mini-service-status.degraded {
    background: rgba(255, 152, 0, 0.2);
    color: #ff9800;
}

.mini-service-status.down {
    background: rgba(244, 67, 54, 0.2);
    color: #f44336;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Footer */
.status-footer {
    background: var(--primary-color);
    color: white;
    text-align: center;
    padding: 2rem 0;
    margin-top: 3rem;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.footer-links {
    display: flex;
    gap: 1.5rem;
}

.footer-links a {
    color: white;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .header-content h1 {
        font-size: 2rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .footer-links {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .metrics-grid {
        grid-template-columns: 1fr;
    }

    .metric-value {
        font-size: 2rem;
    }
}

/* Mermaid Status Light Animations */
.mermaid-container .mermaid {
    transition: all 0.3s ease;
}

/* Status light pulse animation for operational services */
@keyframes statusPulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Status light blink animation for degraded services */
@keyframes statusBlink {
    0% { opacity: 1; }
    25% { opacity: 0.5; }
    50% { opacity: 1; }
    75% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* Status light urgent blink for outage services */
@keyframes statusUrgent {
    0% { opacity: 1; }
    20% { opacity: 0.3; }
    40% { opacity: 1; }
    60% { opacity: 0.3; }
    80% { opacity: 1; }
    100% { opacity: 0.3; }
}

/* Apply animations to Mermaid diagram elements */
.mermaid .node rect,
.mermaid .node circle,
.mermaid .node polygon {
    transition: all 0.3s ease;
}

/* Enhanced service card animations */
.service-card {
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.service-card:hover::before {
    left: 100%;
}

/* Status indicator enhancements */
.service-status {
    position: relative;
    overflow: hidden;
}

.service-status.operational {
    animation: statusPulse 3s infinite ease-in-out;
}

.service-status.degraded {
    animation: statusBlink 2s infinite ease-in-out;
}

.service-status.outage {
    animation: statusUrgent 1s infinite ease-in-out;
}

/* Section Styling */
.metrics-section, .services-section, .activity-section, .architecture-section {
    margin: 2rem 0;
    padding: 1.5rem;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
}

.metrics-section h2, .services-section h2, .activity-section h2, .architecture-section h2 {
    margin: 0 0 1.5rem 0;
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 600;
}

/* Enhanced metric cards for Celery data */
.metric-card.celery-metric {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.metric-card.celery-metric .metric-value {
    color: white;
}

.metric-card.celery-metric .metric-label {
    color: rgba(255, 255, 255, 0.8);
}

/* Celery service cards styling */
.service-card[id*="celery"] {
    border-left: 4px solid #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.service-card[id*="celery"] .service-icon {
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
}

/* Task success rate color coding */
.metric-value.success-rate-excellent {
    color: #28a745;
}

.metric-value.success-rate-good {
    color: #17a2b8;
}

.metric-value.success-rate-warning {
    color: #ffc107;
}

.metric-value.success-rate-poor {
    color: #dc3545;
}

/* Worker count indicators */
.metric-value.workers-healthy {
    color: #28a745;
}

.metric-value.workers-warning {
    color: #ffc107;
}

.metric-value.workers-critical {
    color: #dc3545;
}
