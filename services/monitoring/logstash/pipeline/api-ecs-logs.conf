# Logstash pipeline configuration for TurdParty API ECS logs
# Processes ECS-formatted API request logs and WebSocket events

input {
  # Read from Docker container logs
  beats {
    port => 5044
  }
  
  # Alternative: Read from file if using file logging
  file {
    path => "/app/logs/api-ecs.log"
    start_position => "beginning"
    codec => "json"
    tags => ["api", "ecs", "file"]
  }
  
  # Read from Docker stdout/stderr
  tcp {
    port => 5000
    codec => json_lines
    tags => ["api", "ecs", "tcp"]
  }
}

filter {
  # Parse JSON logs
  if [message] {
    json {
      source => "message"
      target => "parsed"
    }
    
    # If parsing successful, move parsed fields to root
    if [parsed] {
      mutate {
        add_field => { "[@timestamp]" => "%{[parsed][@timestamp]}" }
        add_field => { "[ecs][version]" => "%{[parsed][ecs][version]}" }
        add_field => { "[event][kind]" => "%{[parsed][event][kind]}" }
        add_field => { "[event][category]" => "%{[parsed][event][category]}" }
        add_field => { "[event][type]" => "%{[parsed][event][type]}" }
        add_field => { "[event][outcome]" => "%{[parsed][event][outcome]}" }
        add_field => { "[event][duration]" => "%{[parsed][event][duration]}" }
        add_field => { "[service][name]" => "%{[parsed][service][name]}" }
        add_field => { "[service][type]" => "%{[parsed][service][type]}" }
        add_field => { "[service][version]" => "%{[parsed][service][version]}" }
      }
      
      # HTTP fields
      if [parsed][http] {
        mutate {
          add_field => { "[http][request][method]" => "%{[parsed][http][request][method]}" }
          add_field => { "[http][response][status_code]" => "%{[parsed][http][response][status_code]}" }
          add_field => { "[http][version]" => "%{[parsed][http][version]}" }
        }
      }
      
      # URL fields
      if [parsed][url] {
        mutate {
          add_field => { "[url][path]" => "%{[parsed][url][path]}" }
          add_field => { "[url][query]" => "%{[parsed][url][query]}" }
          add_field => { "[url][full]" => "%{[parsed][url][full]}" }
          add_field => { "[url][scheme]" => "%{[parsed][url][scheme]}" }
          add_field => { "[url][domain]" => "%{[parsed][url][domain]}" }
        }
      }
      
      # Client fields
      if [parsed][client] {
        mutate {
          add_field => { "[client][ip]" => "%{[parsed][client][ip]}" }
          add_field => { "[client][port]" => "%{[parsed][client][port]}" }
        }
      }
      
      # User agent
      if [parsed][user_agent] {
        mutate {
          add_field => { "[user_agent][original]" => "%{[parsed][user_agent][original]}" }
        }
      }
      
      # Labels and tags
      if [parsed][labels] {
        mutate {
          add_field => { "[labels][request_id]" => "%{[parsed][labels][request_id]}" }
          add_field => { "[labels][api_version]" => "%{[parsed][labels][api_version]}" }
          add_field => { "[labels][environment]" => "%{[parsed][labels][environment]}" }
          add_field => { "[labels][vm_id]" => "%{[parsed][labels][vm_id]}" }
          add_field => { "[labels][connection_type]" => "%{[parsed][labels][connection_type]}" }
          add_field => { "[labels][connection_id]" => "%{[parsed][labels][connection_id]}" }
        }
      }
      
      # Performance metrics
      if [parsed][performance] {
        mutate {
          add_field => { "[performance][response_time_ms]" => "%{[parsed][performance][response_time_ms]}" }
          add_field => { "[performance][slow_request]" => "%{[parsed][performance][slow_request]}" }
          add_field => { "[performance][status_class]" => "%{[parsed][performance][status_class]}" }
        }
      }
      
      # Error information
      if [parsed][error] {
        mutate {
          add_field => { "[error][message]" => "%{[parsed][error][message]}" }
          add_field => { "[error][type]" => "%{[parsed][error][type]}" }
        }
      }
      
      # WebSocket specific fields
      if [parsed][websocket] {
        mutate {
          add_field => { "[websocket][data_size]" => "%{[parsed][websocket][data_size]}" }
          add_field => { "[websocket][message_type]" => "%{[parsed][websocket][message_type]}" }
        }
      }
      
      # Copy tags from parsed data
      if [parsed][tags] {
        mutate {
          add_field => { "tags" => "%{[parsed][tags]}" }
        }
      }
      
      # Remove the parsed field to avoid duplication
      mutate {
        remove_field => ["parsed", "message"]
      }
    }
  }
  
  # Add additional processing fields
  mutate {
    add_field => { "[host][name]" => "turdparty-api" }
    add_field => { "[log][level]" => "info" }
  }
  
  # Convert numeric fields
  if [event][duration] {
    mutate {
      convert => { "[event][duration]" => "integer" }
    }
  }
  
  if [http][response][status_code] {
    mutate {
      convert => { "[http][response][status_code]" => "integer" }
    }
  }
  
  if [performance][response_time_ms] {
    mutate {
      convert => { "[performance][response_time_ms]" => "float" }
    }
  }
  
  # Add geo-location for client IPs (optional)
  if [client][ip] and [client][ip] != "unknown" and [client][ip] !~ /^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.|127\.)/ {
    geoip {
      source => "[client][ip]"
      target => "[client][geo]"
    }
  }
  
  # Categorize API endpoints
  if [url][path] {
    if [url][path] =~ /^\/api\/v1\/vms\/.*\/metrics\/stream/ {
      mutate {
        add_field => { "[api][category]" => "vm_metrics" }
        add_field => { "[api][subcategory]" => "websocket_stream" }
      }
    } else if [url][path] =~ /^\/api\/v1\/vms\/.*\/commands\/execute/ {
      mutate {
        add_field => { "[api][category]" => "vm_commands" }
        add_field => { "[api][subcategory]" => "websocket_execution" }
      }
    } else if [url][path] =~ /^\/api\/v1\/vms\/.*\/files/ {
      mutate {
        add_field => { "[api][category]" => "vm_files" }
        add_field => { "[api][subcategory]" => "file_operations" }
      }
    } else if [url][path] =~ /^\/api\/v1\/vms/ {
      mutate {
        add_field => { "[api][category]" => "vm_management" }
        add_field => { "[api][subcategory]" => "rest_api" }
      }
    } else {
      mutate {
        add_field => { "[api][category]" => "general" }
        add_field => { "[api][subcategory]" => "other" }
      }
    }
  }
  
  # Add alert conditions
  if [performance][response_time_ms] and [performance][response_time_ms] > 5000 {
    mutate {
      add_field => { "[alert][type]" => "slow_response" }
      add_field => { "[alert][severity]" => "warning" }
    }
  }
  
  if [http][response][status_code] and [http][response][status_code] >= 500 {
    mutate {
      add_field => { "[alert][type]" => "server_error" }
      add_field => { "[alert][severity]" => "error" }
    }
  }
  
  if [http][response][status_code] and [http][response][status_code] >= 400 and [http][response][status_code] < 500 {
    mutate {
      add_field => { "[alert][type]" => "client_error" }
      add_field => { "[alert][severity]" => "warning" }
    }
  }
}

output {
  # Send to Elasticsearch with different indexes based on log type
  if "websocket" in [tags] {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "turdparty-websocket-logs-%{+YYYY.MM.dd}"
      template_name => "turdparty-websocket"
      template_pattern => "turdparty-websocket-*"
      template => "/usr/share/logstash/templates/websocket-template.json"
    }
  } else if "api" in [tags] {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "turdparty-api-logs-%{+YYYY.MM.dd}"
      template_name => "turdparty-api"
      template_pattern => "turdparty-api-*"
      template => "/usr/share/logstash/templates/api-template.json"
    }
  } else {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "turdparty-general-logs-%{+YYYY.MM.dd}"
    }
  }
  
  # Debug output (remove in production)
  if [log][level] == "debug" {
    stdout {
      codec => rubydebug
    }
  }
}
