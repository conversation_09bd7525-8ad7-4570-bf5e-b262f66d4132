#!/usr/bin/env python3
"""
TurdParty VM Monitoring Agent

Lightweight agent that runs inside VMs to collect runtime data
and stream it to the ELK stack for analysis.
"""

import json
import logging
import os
import sys
import time
import threading
from datetime import datetime
from typing import Dict, Any, List, Optional

import psutil
import requests
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

# Configuration
LOGSTASH_HOST = os.getenv("LOGSTASH_HOST", "logstash")
LOGSTASH_PORT = int(os.getenv("LOGSTASH_PORT", "8080"))
VM_ID = os.getenv("TURDPARTY_VM_ID", "unknown")
VM_NAME = os.getenv("TURDPARTY_VM_NAME", "unknown")
WORKFLOW_ID = os.getenv("TURDPARTY_WORKFLOW_ID", "unknown")
COLLECTION_INTERVAL = int(os.getenv("COLLECTION_INTERVAL", "5"))  # seconds
MONITORED_PATHS = os.getenv("MONITORED_PATHS", "/tmp,/home").split(",")

# Logging setup
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("turdparty-vm-agent")


class SystemMetricsCollector:
    """Collects system-level metrics from the VM."""
    
    def collect_cpu_metrics(self) -> Dict[str, Any]:
        """Collect CPU usage metrics."""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            load_avg = os.getloadavg() if hasattr(os, 'getloadavg') else [0, 0, 0]
            
            return {
                "usage": cpu_percent,
                "cores": cpu_count,
                "load_avg_1m": load_avg[0],
                "load_avg_5m": load_avg[1],
                "load_avg_15m": load_avg[2]
            }
        except Exception as e:
            logger.error(f"Error collecting CPU metrics: {e}")
            return {}
    
    def collect_memory_metrics(self) -> Dict[str, Any]:
        """Collect memory usage metrics."""
        try:
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            return {
                "total": memory.total,
                "available": memory.available,
                "used": memory.used,
                "usage": memory.percent,
                "swap_total": swap.total,
                "swap_used": swap.used,
                "swap_usage": swap.percent
            }
        except Exception as e:
            logger.error(f"Error collecting memory metrics: {e}")
            return {}
    
    def collect_disk_metrics(self) -> Dict[str, Any]:
        """Collect disk usage metrics."""
        try:
            disk = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            metrics = {
                "total": disk.total,
                "used": disk.used,
                "free": disk.free,
                "usage": (disk.used / disk.total) * 100
            }
            
            if disk_io:
                metrics.update({
                    "read_bytes": disk_io.read_bytes,
                    "write_bytes": disk_io.write_bytes,
                    "read_count": disk_io.read_count,
                    "write_count": disk_io.write_count
                })
            
            return metrics
        except Exception as e:
            logger.error(f"Error collecting disk metrics: {e}")
            return {}
    
    def collect_network_metrics(self) -> Dict[str, Any]:
        """Collect network usage metrics."""
        try:
            net_io = psutil.net_io_counters()
            connections = psutil.net_connections()
            
            # Count connections by status
            conn_stats = {}
            for conn in connections:
                status = conn.status
                conn_stats[status] = conn_stats.get(status, 0) + 1
            
            return {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv,
                "connections": conn_stats,
                "total_connections": len(connections)
            }
        except Exception as e:
            logger.error(f"Error collecting network metrics: {e}")
            return {}


class ProcessMonitor:
    """Monitors running processes and their activities."""
    
    def get_running_processes(self) -> List[Dict[str, Any]]:
        """Get list of currently running processes."""
        try:
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cmdline', 'cpu_percent', 'memory_percent', 'create_time']):
                try:
                    proc_info = proc.info
                    processes.append({
                        "pid": proc_info['pid'],
                        "name": proc_info['name'],
                        "cmdline": ' '.join(proc_info['cmdline']) if proc_info['cmdline'] else '',
                        "cpu_percent": proc_info['cpu_percent'],
                        "memory_percent": proc_info['memory_percent'],
                        "create_time": proc_info['create_time']
                    })
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            return processes
        except Exception as e:
            logger.error(f"Error collecting process list: {e}")
            return []
    
    def get_suspicious_processes(self, processes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Identify potentially suspicious processes."""
        suspicious = []
        
        # Define suspicious patterns
        suspicious_names = ['nc', 'netcat', 'wget', 'curl', 'python', 'perl', 'ruby', 'bash', 'sh']
        high_cpu_threshold = 80.0
        high_memory_threshold = 50.0
        
        for proc in processes:
            is_suspicious = False
            reasons = []
            
            # Check for suspicious process names
            if any(name in proc['name'].lower() for name in suspicious_names):
                is_suspicious = True
                reasons.append("suspicious_name")
            
            # Check for high resource usage
            if proc['cpu_percent'] > high_cpu_threshold:
                is_suspicious = True
                reasons.append("high_cpu")
            
            if proc['memory_percent'] > high_memory_threshold:
                is_suspicious = True
                reasons.append("high_memory")
            
            # Check for network tools in command line
            if any(tool in proc['cmdline'].lower() for tool in ['wget', 'curl', 'nc ', 'netcat']):
                is_suspicious = True
                reasons.append("network_tool")
            
            if is_suspicious:
                proc['suspicious_reasons'] = reasons
                suspicious.append(proc)
        
        return suspicious


class FileSystemMonitor(FileSystemEventHandler):
    """Monitors file system events."""
    
    def __init__(self, elk_streamer):
        self.elk_streamer = elk_streamer
        self.event_buffer = []
        self.buffer_lock = threading.Lock()
    
    def on_any_event(self, event):
        """Handle any file system event."""
        try:
            if event.is_directory:
                return
            
            event_data = {
                "event_type": event.event_type,
                "src_path": event.src_path,
                "is_directory": event.is_directory,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            if hasattr(event, 'dest_path'):
                event_data["dest_path"] = event.dest_path
            
            with self.buffer_lock:
                self.event_buffer.append(event_data)
                
                # Send events in batches
                if len(self.event_buffer) >= 10:
                    self._flush_events()
        
        except Exception as e:
            logger.error(f"Error handling file system event: {e}")
    
    def _flush_events(self):
        """Send buffered events to ELK."""
        if not self.event_buffer:
            return
        
        events_to_send = self.event_buffer.copy()
        self.event_buffer.clear()
        
        self.elk_streamer.send_file_events(events_to_send)


class ELKStreamer:
    """Handles streaming data to ELK stack."""
    
    def __init__(self):
        self.logstash_url = f"http://{LOGSTASH_HOST}:{LOGSTASH_PORT}"
        self.session = requests.Session()
    
    def send_metrics(self, metrics_data: Dict[str, Any]) -> bool:
        """Send system metrics to ELK."""
        try:
            elk_data = self._create_elk_event("vm_metrics", metrics_data)
            return self._send_to_logstash(elk_data)
        except Exception as e:
            logger.error(f"Error sending metrics to ELK: {e}")
            return False
    
    def send_process_data(self, process_data: Dict[str, Any]) -> bool:
        """Send process information to ELK."""
        try:
            elk_data = self._create_elk_event("process_monitoring", process_data)
            return self._send_to_logstash(elk_data)
        except Exception as e:
            logger.error(f"Error sending process data to ELK: {e}")
            return False
    
    def send_file_events(self, file_events: List[Dict[str, Any]]) -> bool:
        """Send file system events to ELK."""
        try:
            elk_data = self._create_elk_event("file_monitoring", {"events": file_events})
            return self._send_to_logstash(elk_data)
        except Exception as e:
            logger.error(f"Error sending file events to ELK: {e}")
            return False
    
    def _create_elk_event(self, event_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Create ECS-compliant event structure."""
        return {
            "@timestamp": datetime.utcnow().isoformat(),
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "metric" if event_type == "vm_metrics" else "event",
                "category": ["host"] if event_type == "vm_metrics" else ["process"],
                "type": ["info"],
                "action": event_type,
                "dataset": "turdparty.vm_monitoring"
            },
            "service": {
                "name": "turdparty-vm-agent",
                "type": "monitoring",
                "version": "1.0.0"
            },
            "host": {
                "name": VM_NAME
            },
            "turdparty": {
                "vm_id": VM_ID,
                "vm_name": VM_NAME,
                "workflow_id": WORKFLOW_ID,
                "phase": "runtime"
            },
            "vm": {
                "name": VM_NAME,
                "status": "running"
            },
            **data
        }
    
    def _send_to_logstash(self, data: Dict[str, Any]) -> bool:
        """Send data to Logstash HTTP input."""
        try:
            response = self.session.post(
                self.logstash_url,
                json=data,
                headers={"Content-Type": "application/json"},
                timeout=5
            )
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Failed to send data to Logstash: {e}")
            return False


class VMMonitoringAgent:
    """Main VM monitoring agent."""
    
    def __init__(self):
        self.metrics_collector = SystemMetricsCollector()
        self.process_monitor = ProcessMonitor()
        self.elk_streamer = ELKStreamer()
        self.running = False
        self.observer = None
    
    def start(self):
        """Start the monitoring agent."""
        logger.info(f"Starting TurdParty VM Monitoring Agent for VM: {VM_NAME}")
        logger.info(f"Workflow ID: {WORKFLOW_ID}, Collection interval: {COLLECTION_INTERVAL}s")
        
        self.running = True
        
        # Start file system monitoring
        self._start_file_monitoring()
        
        # Start metrics collection loop
        self._start_metrics_collection()
    
    def stop(self):
        """Stop the monitoring agent."""
        logger.info("Stopping VM monitoring agent")
        self.running = False
        
        if self.observer:
            self.observer.stop()
            self.observer.join()
    
    def _start_file_monitoring(self):
        """Start file system monitoring."""
        try:
            self.observer = Observer()
            event_handler = FileSystemMonitor(self.elk_streamer)
            
            for path in MONITORED_PATHS:
                if os.path.exists(path):
                    self.observer.schedule(event_handler, path, recursive=True)
                    logger.info(f"Monitoring file system path: {path}")
            
            self.observer.start()
        except Exception as e:
            logger.error(f"Error starting file monitoring: {e}")
    
    def _start_metrics_collection(self):
        """Start periodic metrics collection."""
        while self.running:
            try:
                # Collect system metrics
                metrics = {
                    "host": {
                        "cpu": self.metrics_collector.collect_cpu_metrics(),
                        "memory": self.metrics_collector.collect_memory_metrics(),
                        "disk": self.metrics_collector.collect_disk_metrics(),
                        "network": self.metrics_collector.collect_network_metrics()
                    }
                }
                
                # Send metrics to ELK
                self.elk_streamer.send_metrics(metrics)
                
                # Collect and analyze processes
                processes = self.process_monitor.get_running_processes()
                suspicious_processes = self.process_monitor.get_suspicious_processes(processes)
                
                process_data = {
                    "process": {
                        "count": len(processes),
                        "suspicious_count": len(suspicious_processes),
                        "suspicious_processes": suspicious_processes[:10]  # Limit to top 10
                    }
                }
                
                # Send process data to ELK
                self.elk_streamer.send_process_data(process_data)
                
                logger.debug(f"Collected metrics: {len(processes)} processes, {len(suspicious_processes)} suspicious")
                
            except Exception as e:
                logger.error(f"Error in metrics collection: {e}")
            
            time.sleep(COLLECTION_INTERVAL)


def main():
    """Main entry point."""
    try:
        agent = VMMonitoringAgent()
        agent.start()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
        agent.stop()
    except Exception as e:
        logger.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
