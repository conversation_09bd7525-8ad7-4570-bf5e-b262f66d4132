# Logstash pipeline for TurdParty VM runtime data processing

input {
  # Beats input for Fibratus data
  beats {
    port => 5044
    host => "0.0.0.0"
  }
  
  # HTTP input for direct API submissions
  http {
    port => 8080
    host => "0.0.0.0"
    codec => json
    additional_codecs => {
      "application/json" => "json"
    }
  }
  
  # TCP input for syslog-style data
  tcp {
    port => 5000
    codec => json_lines
  }
}

filter {
  # Add common fields
  mutate {
    add_field => {
      "[@metadata][pipeline]" => "turdparty"
      "[@metadata][version]" => "1.0"
    }
  }
  
  # Parse timestamp if present
  if [timestamp] {
    date {
      match => [ "timestamp", "ISO8601", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd'T'HH:mm:ss.SSSZ" ]
      target => "@timestamp"
    }
  }
  
  # Process Fibratus events
  if [agent][type] == "fibratus" {
    mutate {
      add_field => { "event_source" => "fibratus" }
      add_tag => [ "fibratus", "vm_monitoring" ]
    }
    
    # Parse process information
    if [process] {
      mutate {
        add_field => {
          "process_name" => "%{[process][name]}"
          "process_pid" => "%{[process][pid]}"
          "process_command" => "%{[process][command_line]}"
        }
      }
    }
    
    # Parse file operations
    if [file] {
      mutate {
        add_field => {
          "file_path" => "%{[file][path]}"
          "file_operation" => "%{[file][operation]}"
        }
      }
    }
    
    # Parse network operations
    if [network] {
      mutate {
        add_field => {
          "network_protocol" => "%{[network][protocol]}"
          "network_src_ip" => "%{[network][src_ip]}"
          "network_dst_ip" => "%{[network][dst_ip]}"
          "network_src_port" => "%{[network][src_port]}"
          "network_dst_port" => "%{[network][dst_port]}"
        }
      }
    }
  }
  
  # Process VM system logs
  if [source] == "vm_system" {
    mutate {
      add_field => { "event_source" => "vm_system" }
      add_tag => [ "vm_system", "system_monitoring" ]
    }
    
    # Parse system metrics
    if [cpu_usage] {
      mutate {
        convert => { "cpu_usage" => "float" }
      }
    }
    
    if [memory_usage] {
      mutate {
        convert => { "memory_usage" => "float" }
      }
    }
    
    if [disk_usage] {
      mutate {
        convert => { "disk_usage" => "float" }
      }
    }
  }
  
  # Process TurdParty workflow events
  if [source] == "turdparty_workflow" {
    mutate {
      add_field => { "event_source" => "turdparty_workflow" }
      add_tag => [ "turdparty", "workflow" ]
    }
    
    # Parse workflow information
    if [workflow_job_id] {
      mutate {
        add_field => { "workflow_id" => "%{workflow_job_id}" }
      }
    }
    
    if [vm_instance_id] {
      mutate {
        add_field => { "vm_id" => "%{vm_instance_id}" }
      }
    }
  }
  
  # Add geolocation for IP addresses (if available)
  if [network_src_ip] and [network_src_ip] !~ /^(10\.|192\.168\.|172\.(1[6-9]|2[0-9]|3[01])\.)/ {
    geoip {
      source => "network_src_ip"
      target => "src_geoip"
    }
  }
  
  if [network_dst_ip] and [network_dst_ip] !~ /^(10\.|192\.168\.|172\.(1[6-9]|2[0-9]|3[01])\.)/ {
    geoip {
      source => "network_dst_ip"
      target => "dst_geoip"
    }
  }
  
  # Remove sensitive or unnecessary fields
  mutate {
    remove_field => [ "headers", "host" ]
  }
}

output {
  # Route to install-time ECS index
  if [turdparty][phase] == "install" {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "turdparty-install-ecs-%{+YYYY.MM.dd}"
      document_type => "_doc"
      manage_template => true
      template_name => "turdparty-install-ecs"
      template_pattern => "turdparty-install-ecs-*"
      template => "/usr/share/logstash/templates/turdparty-install-template.json"
      template_overwrite => true
    }
  }

  # Route to runtime ECS index
  else if [turdparty][phase] == "runtime" {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "turdparty-runtime-ecs-%{+YYYY.MM.dd}"
      document_type => "_doc"
      manage_template => true
      template_name => "turdparty-runtime-ecs"
      template_pattern => "turdparty-runtime-ecs-*"
      template => "/usr/share/logstash/templates/turdparty-runtime-template.json"
      template_overwrite => true
    }
  }

  # Fallback for other events
  else {
    elasticsearch {
      hosts => ["elasticsearch:9200"]
      index => "turdparty-general-%{+YYYY.MM.dd}"
      document_type => "_doc"
    }
  }

  # Debug output for development
  if [@metadata][debug] {
    stdout {
      codec => rubydebug
    }
  }
}
