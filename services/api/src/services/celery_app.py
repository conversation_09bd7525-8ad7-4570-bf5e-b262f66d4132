"""Celery application configuration."""

import os
from celery import Celery

# Redis configuration for Celery
REDIS_HOST = os.getenv("REDIS_HOST", "cache")
REDIS_PORT = int(os.getenv("REDIS_PORT", "6379"))
REDIS_DB = int(os.getenv("REDIS_DB", "0"))

CELERY_BROKER_URL = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}"
CELERY_RESULT_BACKEND = f"redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB + 1}"

# Global Celery app instance
celery_app: Celery = None


def init_celery() -> Celery:
    """Initialize Celery application."""
    global celery_app
    
    celery_app = Celery(
        "turdparty",
        broker=CELERY_BROKER_URL,
        backend=CELERY_RESULT_BACKEND,
        include=[
            "services.workers.tasks.file_operations",
            "services.workers.tasks.vm_management", 
            "services.workers.tasks.injection_tasks"
        ]
    )
    
    # Celery configuration
    celery_app.conf.update(
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        enable_utc=True,
        task_track_started=True,
        task_time_limit=30 * 60,  # 30 minutes
        task_soft_time_limit=25 * 60,  # 25 minutes
        worker_prefetch_multiplier=1,
        task_acks_late=True,
        worker_max_tasks_per_child=1000,
        task_routes={
            "services.workers.tasks.file_operations.*": {"queue": "file_ops"},
            "services.workers.tasks.vm_management.*": {"queue": "vm_ops"},
            "services.workers.tasks.injection_tasks.*": {"queue": "injection_ops"},
        },
        task_default_queue="default",
        task_default_exchange="default",
        task_default_routing_key="default",
    )
    
    return celery_app


def get_celery_app() -> Celery:
    """Get the Celery application instance."""
    if celery_app is None:
        raise RuntimeError("Celery app not initialized")
    return celery_app
