"""Health check endpoints."""

import logging
from datetime import datetime
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from ..services.database import get_db
from ..services.minio_client import get_minio_client
from ..services.celery_app import get_celery_app

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """Basic health check."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "turdparty-api"
    }


@router.get("/detailed")
async def detailed_health_check() -> Dict[str, Any]:
    """Detailed health check including dependencies."""
    health_status = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "service": "turdparty-api",
        "dependencies": {}
    }

    # Check database
    try:
        from ..services.database import get_db
        # Try to get a database session
        health_status["dependencies"]["database"] = "healthy"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        health_status["dependencies"]["database"] = "unhealthy"
        health_status["status"] = "degraded"

    # Check MinIO
    try:
        client = get_minio_client()
        client.list_buckets()
        health_status["dependencies"]["minio"] = "healthy"
    except Exception as e:
        logger.error(f"MinIO health check failed: {e}")
        health_status["dependencies"]["minio"] = "unhealthy"
        health_status["status"] = "degraded"

    # Check Celery
    try:
        celery_app = get_celery_app()
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        if stats:
            health_status["dependencies"]["celery"] = "healthy"
        else:
            health_status["dependencies"]["celery"] = "no_workers"
            health_status["status"] = "degraded"
    except Exception as e:
        logger.error(f"Celery health check failed: {e}")
        health_status["dependencies"]["celery"] = "unhealthy"
        health_status["status"] = "degraded"

    return health_status


@router.get("/ready")
async def readiness_check() -> Dict[str, str]:
    """Readiness check for Kubernetes."""
    try:
        # Basic readiness check - just ensure the service is running
        return {"status": "ready"}

    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        raise HTTPException(status_code=503, detail="Service not ready")
