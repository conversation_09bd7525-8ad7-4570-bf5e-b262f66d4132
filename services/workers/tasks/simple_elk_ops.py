"""
Simplified ELK integration for TurdParty workflow.
Generates mock ECS data for demo purposes.
"""

import logging
import json
import time
import uuid
import requests
from datetime import datetime
from typing import Dict, Any, List
from celery import shared_task
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)

@shared_task(bind=True, name="services.workers.tasks.simple_elk_ops.stream_workflow_event")
def stream_workflow_event(self, workflow_job_id: str, event_action: str, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Stream workflow event to Elasticsearch.
    """
    try:
        logger.info(f"Streaming workflow event: {event_action} for {workflow_job_id}")
        
        # Create ECS-compliant event
        ecs_event = {
            "@timestamp": datetime.utcnow().isoformat() + "Z",
            "ecs": {"version": "8.11.0"},
            "event": {
                "kind": "event",
                "category": ["process"],
                "type": ["start"],
                "action": event_action,
                "outcome": event_data.get("outcome", "success"),
                "duration": int(time.time() * 1000000)  # microseconds
            },
            "service": {
                "name": "turdparty-workflow",
                "type": "orchestrator",
                "version": "1.0.0"
            },
            "workflow": {
                "job_id": workflow_job_id,
                "action": event_action,
                "data": event_data
            },
            "file_uuid": event_data.get("file_uuid", ""),
            "vm_id": event_data.get("vm_id", ""),
            "labels": {
                "workflow_id": workflow_job_id,
                "environment": "development"
            },
            "tags": ["workflow", "turdparty", "vm-processing"]
        }
        
        # Send to Elasticsearch
        es_url = f"http://elasticsearch:9200/turdparty-workflow-ecs-{datetime.now().strftime('%Y.%m.%d')}/_doc"
        
        try:
            response = requests.post(
                es_url,
                json=ecs_event,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                logger.info(f"Workflow event sent successfully: {event_action}")
            else:
                logger.warning(f"Failed to send workflow event: {response.status_code}")
                
        except Exception as e:
            logger.warning(f"Failed to send event to Elasticsearch: {e}")
        
        return {
            "success": True,
            "event_action": event_action,
            "workflow_job_id": workflow_job_id
        }
        
    except Exception as e:
        logger.error(f"Failed to stream workflow event: {e}")
        return {
            "success": False,
            "error": str(e)
        }

@shared_task(bind=True, name="services.workers.tasks.simple_elk_ops.generate_installation_events")
def generate_installation_events(self, file_uuid: str, vm_id: str, workflow_job_id: str) -> Dict[str, Any]:
    """
    Generate mock installation events for Notepad++.
    """
    try:
        logger.info(f"Generating installation events for {file_uuid}")
        
        # Mock installation events for Notepad++
        events = []
        base_time = datetime.utcnow()
        
        # File creation events
        file_events = [
            {"path": "C:\\Program Files\\Notepad++\\notepad++.exe", "size": 4567890, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\SciLexer.dll", "size": 1234567, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\langs.xml", "size": 45678, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\stylers.xml", "size": 23456, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\config.xml", "size": 12345, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\shortcuts.xml", "size": 8901, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\contextMenu.xml", "size": 5678, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\plugins\\DSpellCheck.dll", "size": 234567, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\plugins\\NppConverter.dll", "size": 123456, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\plugins\\mimeTools.dll", "size": 98765, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\themes\\DarkModeDefault.xml", "size": 15432, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\autoCompletion\\c.xml", "size": 87654, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\autoCompletion\\python.xml", "size": 76543, "action": "file_created"},
            {"path": "C:\\Program Files\\Notepad++\\localization\\english.xml", "size": 34567, "action": "file_created"},
            {"path": "C:\\Users\\<USER>\\Desktop\\Notepad++.lnk", "size": 1024, "action": "file_created"}
        ]
        
        # Registry events
        registry_events = [
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Notepad++", "action": "registry_key_created"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Notepad++\\version", "value": "8.5.8", "action": "registry_value_set"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Notepad++\\installPath", "value": "C:\\Program Files\\Notepad++", "action": "registry_value_set"},
            {"key": "HKEY_CURRENT_USER\\SOFTWARE\\Classes\\.txt\\OpenWithProgids\\Notepad++_file", "action": "registry_key_created"},
            {"key": "HKEY_CURRENT_USER\\SOFTWARE\\Classes\\Applications\\notepad++.exe", "action": "registry_key_created"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++", "action": "registry_key_created"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\DisplayName", "value": "Notepad++ (64-bit x64)", "action": "registry_value_set"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\DisplayVersion", "value": "8.5.8", "action": "registry_value_set"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\Publisher", "value": "Notepad++ Team", "action": "registry_value_set"},
            {"key": "HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\Notepad++\\InstallLocation", "value": "C:\\Program Files\\Notepad++\\", "action": "registry_value_set"}
        ]
        
        # Process events
        process_events = [
            {"name": "npp.8.5.8.Installer.x64.exe", "pid": 1234, "command": "npp.8.5.8.Installer.x64.exe /S", "action": "process_start"},
            {"name": "msiexec.exe", "pid": 1567, "command": "msiexec.exe /i notepadpp.msi /quiet", "action": "process_start"},
            {"name": "notepad++.exe", "pid": 1890, "command": "notepad++.exe", "action": "process_start"}
        ]
        
        # Generate events with timestamps
        event_counter = 0
        for file_event in file_events:
            event_counter += 1
            timestamp = (base_time.timestamp() + event_counter * 2) * 1000000  # microseconds
            
            ecs_event = {
                "@timestamp": datetime.fromtimestamp(timestamp / 1000000).isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["file"],
                    "type": ["creation"],
                    "action": file_event["action"],
                    "outcome": "success",
                    "duration": 1000000  # 1 second in microseconds
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring",
                    "version": "1.0.0"
                },
                "file": {
                    "path": file_event["path"],
                    "size": file_event["size"],
                    "type": "file"
                },
                "file_uuid": file_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "labels": {
                    "workflow_id": workflow_job_id,
                    "environment": "development"
                },
                "tags": ["installation", "file-creation", "notepad++"]
            }
            
            events.append(ecs_event)
        
        # Add registry events
        for reg_event in registry_events:
            event_counter += 1
            timestamp = (base_time.timestamp() + event_counter * 2) * 1000000
            
            ecs_event = {
                "@timestamp": datetime.fromtimestamp(timestamp / 1000000).isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["configuration"],
                    "type": ["change"],
                    "action": reg_event["action"],
                    "outcome": "success",
                    "duration": 500000  # 0.5 seconds
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring",
                    "version": "1.0.0"
                },
                "registry": {
                    "key": reg_event["key"],
                    "value": reg_event.get("value", ""),
                    "type": "REG_SZ"
                },
                "file_uuid": file_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "labels": {
                    "workflow_id": workflow_job_id,
                    "environment": "development"
                },
                "tags": ["installation", "registry-change", "notepad++"]
            }
            
            events.append(ecs_event)
        
        # Add process events
        for proc_event in process_events:
            event_counter += 1
            timestamp = (base_time.timestamp() + event_counter * 2) * 1000000
            
            ecs_event = {
                "@timestamp": datetime.fromtimestamp(timestamp / 1000000).isoformat() + "Z",
                "ecs": {"version": "8.11.0"},
                "event": {
                    "kind": "event",
                    "category": ["process"],
                    "type": ["start"],
                    "action": proc_event["action"],
                    "outcome": "success",
                    "duration": 2000000  # 2 seconds
                },
                "service": {
                    "name": "turdparty-vm-agent",
                    "type": "monitoring",
                    "version": "1.0.0"
                },
                "process": {
                    "name": proc_event["name"],
                    "pid": proc_event["pid"],
                    "command_line": proc_event["command"]
                },
                "file_uuid": file_uuid,
                "vm_id": vm_id,
                "workflow_job_id": workflow_job_id,
                "host": {
                    "name": f"turdparty-vm-{vm_id[:8]}",
                    "id": vm_id
                },
                "labels": {
                    "workflow_id": workflow_job_id,
                    "environment": "development"
                },
                "tags": ["installation", "process-execution", "notepad++"]
            }
            
            events.append(ecs_event)
        
        # Send events to Elasticsearch
        es_url_install = f"http://elasticsearch:9200/turdparty-install-ecs-{datetime.now().strftime('%Y.%m.%d')}/_doc"
        es_url_runtime = f"http://elasticsearch:9200/turdparty-runtime-ecs-{datetime.now().strftime('%Y.%m.%d')}/_doc"
        
        sent_count = 0
        for event in events:
            try:
                # Send to appropriate index based on event type
                if event["event"]["category"][0] in ["file", "configuration"]:
                    url = es_url_install
                else:
                    url = es_url_runtime
                
                response = requests.post(
                    url,
                    json=event,
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
                
                if response.status_code in [200, 201]:
                    sent_count += 1
                
            except Exception as e:
                logger.warning(f"Failed to send event to Elasticsearch: {e}")
        
        logger.info(f"Generated and sent {sent_count}/{len(events)} installation events")
        
        return {
            "success": True,
            "events_generated": len(events),
            "events_sent": sent_count,
            "file_uuid": file_uuid
        }
        
    except Exception as e:
        logger.error(f"Failed to generate installation events: {e}")
        return {
            "success": False,
            "error": str(e)
        }
