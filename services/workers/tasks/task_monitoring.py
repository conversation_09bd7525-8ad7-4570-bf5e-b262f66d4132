"""
Task Monitoring and Alerting for TurdParty Scheduled Tasks
Provides monitoring, metrics collection, and alerting for scheduled maintenance tasks
"""

import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict

from celery import current_app
from ..celery_app import app

logger = logging.getLogger(__name__)


@dataclass
class TaskMetrics:
    """
    Metrics for a scheduled task execution.
    
    Attributes:
        task_name: Name of the task
        execution_time: When the task was executed
        duration_seconds: How long the task took
        status: success, failure, or retry
        result: Task result data
        error_message: Error message if failed
        retry_count: Number of retries attempted
    """
    task_name: str
    execution_time: datetime
    duration_seconds: float
    status: str  # success, failure, retry
    result: Optional[Dict[str, Any]] = None
    error_message: Optional[str] = None
    retry_count: int = 0


@dataclass
class SystemAlert:
    """
    System alert for monitoring issues.
    
    Attributes:
        alert_type: Type of alert (health, performance, error)
        severity: critical, high, medium, low
        message: Alert message
        component: System component affected
        timestamp: When the alert was generated
        metadata: Additional alert data
    """
    alert_type: str
    severity: str
    message: str
    component: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


class TaskMonitor:
    """
    Monitor for scheduled task execution and system health.
    
    Collects metrics, generates alerts, and provides monitoring
    capabilities for all scheduled maintenance tasks.
    """
    
    def __init__(self):
        """Initialize the task monitor."""
        self.logger = logging.getLogger("turdparty.task_monitor")
        self._metrics_cache: List[TaskMetrics] = []
        self._alerts_cache: List[SystemAlert] = []
    
    def record_task_execution(
        self,
        task_name: str,
        duration_seconds: float,
        status: str,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        retry_count: int = 0
    ) -> None:
        """
        Record metrics for a task execution.
        
        Args:
            task_name: Name of the executed task
            duration_seconds: Task execution duration
            status: Execution status (success, failure, retry)
            result: Task result data
            error_message: Error message if failed
            retry_count: Number of retries
        """
        metrics = TaskMetrics(
            task_name=task_name,
            execution_time=datetime.utcnow(),
            duration_seconds=duration_seconds,
            status=status,
            result=result,
            error_message=error_message,
            retry_count=retry_count
        )
        
        self._metrics_cache.append(metrics)
        
        # Log the metrics
        self.logger.info(
            f"Task execution recorded: {task_name}",
            extra={
                "task_metrics": asdict(metrics),
                "task_name": task_name,
                "status": status,
                "duration": duration_seconds
            }
        )
        
        # Generate alerts for failures
        if status == "failure":
            self.generate_alert(
                alert_type="task_failure",
                severity="high" if retry_count >= 3 else "medium",
                message=f"Task {task_name} failed: {error_message}",
                component="scheduled_tasks",
                metadata={"task_name": task_name, "retry_count": retry_count}
            )
    
    def generate_alert(
        self,
        alert_type: str,
        severity: str,
        message: str,
        component: str,
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Generate a system alert.
        
        Args:
            alert_type: Type of alert
            severity: Alert severity level
            message: Alert message
            component: Affected component
            metadata: Additional alert data
        """
        alert = SystemAlert(
            alert_type=alert_type,
            severity=severity,
            message=message,
            component=component,
            timestamp=datetime.utcnow(),
            metadata=metadata or {}
        )
        
        self._alerts_cache.append(alert)
        
        # Log the alert
        log_level = {
            "critical": logging.CRITICAL,
            "high": logging.ERROR,
            "medium": logging.WARNING,
            "low": logging.INFO
        }.get(severity, logging.INFO)
        
        self.logger.log(
            log_level,
            f"System alert: {message}",
            extra={
                "alert": asdict(alert),
                "alert_type": alert_type,
                "severity": severity,
                "component": component
            }
        )
    
    def get_task_metrics(
        self,
        task_name: Optional[str] = None,
        hours: int = 24
    ) -> List[TaskMetrics]:
        """
        Get task metrics for the specified period.
        
        Args:
            task_name: Filter by task name (optional)
            hours: Number of hours to look back
            
        Returns:
            List[TaskMetrics]: Filtered task metrics
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        filtered_metrics = [
            m for m in self._metrics_cache
            if m.execution_time >= cutoff_time
        ]
        
        if task_name:
            filtered_metrics = [
                m for m in filtered_metrics
                if m.task_name == task_name
            ]
        
        return filtered_metrics
    
    def get_system_alerts(
        self,
        severity: Optional[str] = None,
        hours: int = 24
    ) -> List[SystemAlert]:
        """
        Get system alerts for the specified period.
        
        Args:
            severity: Filter by severity (optional)
            hours: Number of hours to look back
            
        Returns:
            List[SystemAlert]: Filtered system alerts
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=hours)
        
        filtered_alerts = [
            a for a in self._alerts_cache
            if a.timestamp >= cutoff_time
        ]
        
        if severity:
            filtered_alerts = [
                a for a in filtered_alerts
                if a.severity == severity
            ]
        
        return filtered_alerts
    
    def get_health_summary(self) -> Dict[str, Any]:
        """
        Get a summary of system health based on recent metrics.
        
        Returns:
            Dict[str, Any]: Health summary with metrics and status
        """
        # Get metrics for the last 24 hours
        recent_metrics = self.get_task_metrics(hours=24)
        recent_alerts = self.get_system_alerts(hours=24)
        
        # Calculate success rates
        total_executions = len(recent_metrics)
        successful_executions = len([m for m in recent_metrics if m.status == "success"])
        failed_executions = len([m for m in recent_metrics if m.status == "failure"])
        
        success_rate = (successful_executions / total_executions * 100) if total_executions > 0 else 0
        
        # Count alerts by severity
        alert_counts = {
            "critical": len([a for a in recent_alerts if a.severity == "critical"]),
            "high": len([a for a in recent_alerts if a.severity == "high"]),
            "medium": len([a for a in recent_alerts if a.severity == "medium"]),
            "low": len([a for a in recent_alerts if a.severity == "low"])
        }
        
        # Determine overall health status
        if alert_counts["critical"] > 0 or success_rate < 50:
            health_status = "critical"
        elif alert_counts["high"] > 0 or success_rate < 80:
            health_status = "degraded"
        elif alert_counts["medium"] > 2 or success_rate < 95:
            health_status = "warning"
        else:
            health_status = "healthy"
        
        return {
            "health_status": health_status,
            "success_rate": success_rate,
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "failed_executions": failed_executions,
            "alert_counts": alert_counts,
            "last_updated": datetime.utcnow().isoformat()
        }


# Global task monitor instance
task_monitor = TaskMonitor()


@app.task(bind=True)
def collect_task_metrics(self) -> Dict[str, Any]:
    """
    Collect and analyze task execution metrics.
    
    This task runs periodically to analyze the performance
    of all scheduled tasks and generate health reports.
    
    Returns:
        Dict[str, Any]: Metrics collection results
    """
    try:
        logger.info("Collecting task execution metrics")
        
        # Get Celery task statistics
        inspect = current_app.control.inspect()
        
        # Get active tasks
        active_tasks = inspect.active()
        scheduled_tasks = inspect.scheduled()
        reserved_tasks = inspect.reserved()
        
        # Get worker statistics
        stats = inspect.stats()
        
        metrics_summary = {
            "timestamp": datetime.utcnow().isoformat(),
            "active_tasks": len(active_tasks) if active_tasks else 0,
            "scheduled_tasks": len(scheduled_tasks) if scheduled_tasks else 0,
            "reserved_tasks": len(reserved_tasks) if reserved_tasks else 0,
            "worker_count": len(stats) if stats else 0,
            "health_summary": task_monitor.get_health_summary()
        }
        
        # Log metrics summary
        logger.info(
            "Task metrics collected",
            extra=metrics_summary
        )
        
        return metrics_summary
        
    except Exception as exc:
        logger.error(f"Failed to collect task metrics: {exc}", exc_info=True)
        task_monitor.generate_alert(
            alert_type="monitoring_failure",
            severity="medium",
            message=f"Task metrics collection failed: {exc}",
            component="task_monitor"
        )
        raise


@app.task(bind=True)
def generate_health_alerts(self) -> Dict[str, Any]:
    """
    Generate health alerts based on system metrics.
    
    Analyzes recent system performance and generates
    alerts for any issues that require attention.
    
    Returns:
        Dict[str, Any]: Alert generation results
    """
    try:
        logger.info("Generating health alerts")
        
        health_summary = task_monitor.get_health_summary()
        alerts_generated = []
        
        # Check success rate
        if health_summary["success_rate"] < 80:
            task_monitor.generate_alert(
                alert_type="low_success_rate",
                severity="high" if health_summary["success_rate"] < 50 else "medium",
                message=f"Task success rate is {health_summary['success_rate']:.1f}%",
                component="scheduled_tasks",
                metadata={"success_rate": health_summary["success_rate"]}
            )
            alerts_generated.append("low_success_rate")
        
        # Check for excessive failures
        if health_summary["failed_executions"] > 10:
            task_monitor.generate_alert(
                alert_type="high_failure_count",
                severity="medium",
                message=f"{health_summary['failed_executions']} task failures in last 24h",
                component="scheduled_tasks",
                metadata={"failure_count": health_summary["failed_executions"]}
            )
            alerts_generated.append("high_failure_count")
        
        # Check worker availability
        inspect = current_app.control.inspect()
        active_workers = inspect.active()
        
        if not active_workers:
            task_monitor.generate_alert(
                alert_type="no_active_workers",
                severity="critical",
                message="No active Celery workers detected",
                component="workers"
            )
            alerts_generated.append("no_active_workers")
        
        result = {
            "timestamp": datetime.utcnow().isoformat(),
            "alerts_generated": alerts_generated,
            "health_status": health_summary["health_status"]
        }
        
        logger.info(
            f"Health alert generation completed: {len(alerts_generated)} alerts",
            extra=result
        )
        
        return result
        
    except Exception as exc:
        logger.error(f"Failed to generate health alerts: {exc}", exc_info=True)
        raise
