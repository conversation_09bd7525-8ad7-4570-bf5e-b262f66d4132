"""
Scheduled Maintenance Tasks for TurdParty
Comprehensive scheduled tasks for system health, cleanup, and monitoring
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any

from celery import Task
from ..celery_app import app

logger = logging.getLogger(__name__)


class ScheduledMaintenanceTask(Task):
    """
    Base class for scheduled maintenance tasks.
    
    Provides common functionality for all scheduled maintenance operations
    including error handling, logging, and metrics collection.
    """
    
    def on_success(self, retval: Any, task_id: str, args: tuple, kwargs: dict) -> None:
        """
        Handle successful task completion.
        
        Args:
            retval: Task return value
            task_id: Unique task identifier
            args: Task positional arguments
            kwargs: Task keyword arguments
        """
        logger.info(
            f"Scheduled task {self.name} completed successfully",
            extra={
                "task_id": task_id,
                "task_name": self.name,
                "duration": getattr(self, '_start_time', 0) and time.time() - self._start_time,
                "result": retval
            }
        )
    
    def on_failure(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo) -> None:
        """
        Handle task failure.
        
        Args:
            exc: Exception that caused the failure
            task_id: Unique task identifier
            args: Task positional arguments
            kwargs: Task keyword arguments
            einfo: Exception info
        """
        logger.error(
            f"Scheduled task {self.name} failed",
            exc_info=True,
            extra={
                "task_id": task_id,
                "task_name": self.name,
                "exception": str(exc),
                "exception_type": type(exc).__name__
            }
        )
    
    def on_retry(self, exc: Exception, task_id: str, args: tuple, kwargs: dict, einfo) -> None:
        """
        Handle task retry.
        
        Args:
            exc: Exception that caused the retry
            task_id: Unique task identifier
            args: Task positional arguments
            kwargs: Task keyword arguments
            einfo: Exception info
        """
        logger.warning(
            f"Scheduled task {self.name} retrying",
            extra={
                "task_id": task_id,
                "task_name": self.name,
                "retry_count": self.request.retries,
                "exception": str(exc)
            }
        )


@app.task(bind=True, base=ScheduledMaintenanceTask, max_retries=3)
def vm_pool_health_check(self) -> Dict[str, Any]:
    """
    Comprehensive VM pool health monitoring.
    
    Checks the health of all VMs in the pool, identifies issues,
    and triggers corrective actions when necessary.
    
    Returns:
        Dict[str, Any]: Health check results and metrics
        
    Raises:
        Exception: If health check fails critically
    """
    self._start_time = time.time()
    
    try:
        logger.info("Starting VM pool health check")
        
        # Import here to avoid circular imports
        from .vm_pool_manager import get_pool_status, check_vm_health
        
        # Get current pool status
        pool_status = get_pool_status()
        
        health_results = {
            "timestamp": datetime.utcnow().isoformat(),
            "total_vms": pool_status.get("total_vms", 0),
            "healthy_vms": 0,
            "unhealthy_vms": 0,
            "issues": [],
            "actions_taken": []
        }
        
        # Check each VM's health
        for vm_id in pool_status.get("vm_ids", []):
            try:
                vm_health = check_vm_health(vm_id)
                if vm_health.get("healthy", False):
                    health_results["healthy_vms"] += 1
                else:
                    health_results["unhealthy_vms"] += 1
                    health_results["issues"].append({
                        "vm_id": vm_id,
                        "issue": vm_health.get("issue", "Unknown"),
                        "severity": vm_health.get("severity", "medium")
                    })
                    
                    # Take corrective action for critical issues
                    if vm_health.get("severity") == "critical":
                        logger.warning(f"Taking corrective action for VM {vm_id}")
                        # Trigger VM restart or replacement
                        health_results["actions_taken"].append({
                            "vm_id": vm_id,
                            "action": "restart_scheduled",
                            "reason": vm_health.get("issue")
                        })
                        
            except Exception as e:
                logger.error(f"Failed to check health for VM {vm_id}: {e}")
                health_results["issues"].append({
                    "vm_id": vm_id,
                    "issue": f"Health check failed: {e}",
                    "severity": "high"
                })
        
        # Calculate health percentage
        total_vms = health_results["total_vms"]
        if total_vms > 0:
            health_percentage = (health_results["healthy_vms"] / total_vms) * 100
            health_results["health_percentage"] = health_percentage
            
            # Alert if health is below threshold
            if health_percentage < 80:
                logger.warning(
                    f"VM pool health below threshold: {health_percentage:.1f}%",
                    extra={"health_percentage": health_percentage, "threshold": 80}
                )
        
        logger.info(
            f"VM pool health check completed: {health_results['healthy_vms']}/{total_vms} healthy",
            extra=health_results
        )
        
        return health_results
        
    except Exception as exc:
        logger.error(f"VM pool health check failed: {exc}", exc_info=True)
        self.retry(countdown=60, exc=exc)


@app.task(bind=True, base=ScheduledMaintenanceTask, max_retries=5)
def cleanup_orphaned_resources(self) -> Dict[str, Any]:
    """
    Clean up orphaned VMs, files, and other resources.
    
    Identifies and removes resources that are no longer needed,
    including terminated VMs, expired files, and stale database entries.
    
    Returns:
        Dict[str, Any]: Cleanup results and statistics
        
    Raises:
        Exception: If cleanup fails critically
    """
    self._start_time = time.time()
    
    try:
        logger.info("Starting orphaned resource cleanup")
        
        cleanup_results = {
            "timestamp": datetime.utcnow().isoformat(),
            "orphaned_vms_removed": 0,
            "expired_files_removed": 0,
            "stale_workflows_cleaned": 0,
            "disk_space_freed_mb": 0,
            "errors": []
        }
        
        # Clean up orphaned VMs (older than 2 hours with no activity)
        try:
            from .vm_management import cleanup_orphaned_vms
            orphaned_vms = cleanup_orphaned_vms(max_age_hours=2)
            cleanup_results["orphaned_vms_removed"] = len(orphaned_vms)
            logger.info(f"Cleaned up {len(orphaned_vms)} orphaned VMs")
        except Exception as e:
            cleanup_results["errors"].append(f"VM cleanup failed: {e}")
            logger.error(f"Failed to cleanup orphaned VMs: {e}")
        
        # Clean up expired files (older than 7 days)
        try:
            from .file_operations import cleanup_expired_files
            expired_files = cleanup_expired_files(max_age_days=7)
            cleanup_results["expired_files_removed"] = len(expired_files)
            cleanup_results["disk_space_freed_mb"] = sum(f.get("size_mb", 0) for f in expired_files)
            logger.info(f"Cleaned up {len(expired_files)} expired files")
        except Exception as e:
            cleanup_results["errors"].append(f"File cleanup failed: {e}")
            logger.error(f"Failed to cleanup expired files: {e}")
        
        # Clean up stale workflow entries (completed > 24 hours ago)
        try:
            from .workflow_orchestrator import cleanup_stale_workflows
            stale_workflows = cleanup_stale_workflows(max_age_hours=24)
            cleanup_results["stale_workflows_cleaned"] = len(stale_workflows)
            logger.info(f"Cleaned up {len(stale_workflows)} stale workflows")
        except Exception as e:
            cleanup_results["errors"].append(f"Workflow cleanup failed: {e}")
            logger.error(f"Failed to cleanup stale workflows: {e}")
        
        # Log summary
        total_items = (
            cleanup_results["orphaned_vms_removed"] +
            cleanup_results["expired_files_removed"] +
            cleanup_results["stale_workflows_cleaned"]
        )
        
        logger.info(
            f"Cleanup completed: {total_items} items removed, "
            f"{cleanup_results['disk_space_freed_mb']:.1f}MB freed",
            extra=cleanup_results
        )
        
        return cleanup_results
        
    except Exception as exc:
        logger.error(f"Resource cleanup failed: {exc}", exc_info=True)
        self.retry(countdown=300, exc=exc)  # Retry after 5 minutes


@app.task(bind=True, base=ScheduledMaintenanceTask, max_retries=3)
def elk_index_management(self) -> Dict[str, Any]:
    """
    Manage Elasticsearch indices for optimal performance.
    
    Performs index maintenance including rotation, optimization,
    and cleanup of old indices to maintain ELK stack performance.
    
    Returns:
        Dict[str, Any]: Index management results
        
    Raises:
        Exception: If index management fails critically
    """
    self._start_time = time.time()
    
    try:
        logger.info("Starting ELK index management")
        
        # Import here to avoid circular imports
        from .elk_integration import (
            get_elasticsearch_client,
            rotate_indices,
            optimize_indices,
            cleanup_old_indices
        )
        
        management_results = {
            "timestamp": datetime.utcnow().isoformat(),
            "indices_rotated": 0,
            "indices_optimized": 0,
            "indices_deleted": 0,
            "disk_space_freed_gb": 0,
            "errors": []
        }
        
        es_client = get_elasticsearch_client()
        
        # Rotate daily indices if needed
        try:
            rotated = rotate_indices(es_client, index_pattern="turdparty-*")
            management_results["indices_rotated"] = len(rotated)
            logger.info(f"Rotated {len(rotated)} indices")
        except Exception as e:
            management_results["errors"].append(f"Index rotation failed: {e}")
            logger.error(f"Failed to rotate indices: {e}")
        
        # Optimize indices older than 1 day
        try:
            optimized = optimize_indices(es_client, max_age_days=1)
            management_results["indices_optimized"] = len(optimized)
            logger.info(f"Optimized {len(optimized)} indices")
        except Exception as e:
            management_results["errors"].append(f"Index optimization failed: {e}")
            logger.error(f"Failed to optimize indices: {e}")
        
        # Delete indices older than 30 days
        try:
            deleted = cleanup_old_indices(es_client, max_age_days=30)
            management_results["indices_deleted"] = len(deleted)
            management_results["disk_space_freed_gb"] = sum(
                idx.get("size_gb", 0) for idx in deleted
            )
            logger.info(f"Deleted {len(deleted)} old indices")
        except Exception as e:
            management_results["errors"].append(f"Index cleanup failed: {e}")
            logger.error(f"Failed to cleanup old indices: {e}")
        
        logger.info(
            f"ELK index management completed: "
            f"{management_results['indices_rotated']} rotated, "
            f"{management_results['indices_optimized']} optimized, "
            f"{management_results['indices_deleted']} deleted",
            extra=management_results
        )
        
        return management_results
        
    except Exception as exc:
        logger.error(f"ELK index management failed: {exc}", exc_info=True)
        self.retry(countdown=600, exc=exc)  # Retry after 10 minutes


@app.task(bind=True, base=ScheduledMaintenanceTask, max_retries=2)
def system_health_report(self) -> Dict[str, Any]:
    """
    Generate comprehensive system health report.
    
    Collects metrics from all system components and generates
    a health report for monitoring and alerting purposes.
    
    Returns:
        Dict[str, Any]: System health report
        
    Raises:
        Exception: If health report generation fails
    """
    self._start_time = time.time()
    
    try:
        logger.info("Generating system health report")
        
        health_report = {
            "timestamp": datetime.utcnow().isoformat(),
            "overall_status": "healthy",
            "components": {},
            "metrics": {},
            "alerts": []
        }
        
        # Check API service health
        try:
            # This would typically make HTTP requests to health endpoints
            health_report["components"]["api"] = {
                "status": "healthy",
                "response_time_ms": 50,
                "last_check": datetime.utcnow().isoformat()
            }
        except Exception as e:
            health_report["components"]["api"] = {
                "status": "unhealthy",
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }
            health_report["alerts"].append(f"API service unhealthy: {e}")
        
        # Check worker health
        try:
            from celery import current_app
            inspect = current_app.control.inspect()
            active_workers = inspect.active()
            
            health_report["components"]["workers"] = {
                "status": "healthy" if active_workers else "unhealthy",
                "active_workers": len(active_workers) if active_workers else 0,
                "last_check": datetime.utcnow().isoformat()
            }
            
            if not active_workers:
                health_report["alerts"].append("No active workers detected")
                
        except Exception as e:
            health_report["components"]["workers"] = {
                "status": "unknown",
                "error": str(e),
                "last_check": datetime.utcnow().isoformat()
            }
        
        # Determine overall status
        component_statuses = [
            comp.get("status") for comp in health_report["components"].values()
        ]
        
        if "unhealthy" in component_statuses:
            health_report["overall_status"] = "unhealthy"
        elif "unknown" in component_statuses:
            health_report["overall_status"] = "degraded"
        
        logger.info(
            f"System health report generated: {health_report['overall_status']}",
            extra=health_report
        )
        
        return health_report
        
    except Exception as exc:
        logger.error(f"System health report generation failed: {exc}", exc_info=True)
        raise  # Don't retry health reports, just fail
