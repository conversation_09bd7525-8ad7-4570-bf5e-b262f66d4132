"""Celery tasks for VM management operations."""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import asyncio
import sys
import os

# Add the workers directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from celery import current_app as celery_app
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker

from .models import VMStatus

logger = logging.getLogger(__name__)

# Database setup for workers
DATABASE_URL = "**********************************************/turdparty"
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.create_vm")
def create_vm(self, vm_id: str, vm_type: str, vm_config: Dict[str, Any]):
    """Create a VM instance (Vagrant or Docker)."""
    try:
        logger.info(f"Creating {vm_type} VM: {vm_id}")

        # Update VM status to creating using raw SQL
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": "creating", "vm_id": vm_id}
            )
            db.commit()

        # Import VM manager here to avoid circular imports
        try:
            import docker

            if vm_type == "docker":
                result = create_docker_vm_simple(vm_config)
            else:
                result = {
                    "success": False,
                    "error": f"VM type {vm_type} not implemented yet",
                    "message": "Only Docker VMs are currently supported"
                }
        except Exception as e:
            result = {
                "success": False,
                "error": str(e),
                "message": "VM creation failed"
            }

        # Update VM status based on result
        with SessionLocal() as db:
            if result["success"]:
                # Calculate termination time (30 minutes from now)
                termination_time = datetime.utcnow() + timedelta(minutes=30)

                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status,
                            vm_id = :vm_id_external,
                            ip_address = :ip_address,
                            ssh_port = :ssh_port,
                            started_at = :started_at,
                            scheduled_termination = :scheduled_termination
                        WHERE id = :vm_id
                    """),
                    {
                        "status": "running",
                        "vm_id_external": result.get("vm_id"),
                        "ip_address": result.get("ip_address"),
                        "ssh_port": result.get("ssh_port"),
                        "started_at": datetime.utcnow(),
                        "scheduled_termination": termination_time,
                        "vm_id": vm_id
                    }
                )

                logger.info(f"VM {vm_id} created successfully")

                # Schedule automatic termination
                terminate_vm.apply_async(
                    args=[vm_id, vm_type],
                    countdown=30 * 60  # 30 minutes
                )

            else:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status, error_message = :error_message
                        WHERE id = :vm_id
                    """),
                    {
                        "status": "failed",
                        "error_message": result.get("error", "VM creation failed"),
                        "vm_id": vm_id
                    }
                )
                logger.error(f"VM {vm_id} creation failed: {result.get('error')}")

            db.commit()

        return {
            "vm_id": vm_id,
            "success": result["success"],
            "message": result.get("message"),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"VM creation task failed: {e}")

        # Update VM status to failed
        try:
            with SessionLocal() as db:
                db.execute(
                    text("""
                        UPDATE vm_instances
                        SET status = :status, error_message = :error_message
                        WHERE id = :vm_id
                    """),
                    {
                        "status": "failed",
                        "error_message": str(e),
                        "vm_id": vm_id
                    }
                )
                db.commit()
        except Exception as db_error:
            logger.error(f"Failed to update VM status: {db_error}")

        raise


def create_docker_vm_simple(vm_config: Dict[str, Any]) -> Dict[str, Any]:
    """Simple Docker VM creation."""
    try:
        import docker

        client = docker.from_env()

        vm_name = vm_config["name"]
        template = vm_config.get("template", "ubuntu:20.04")
        memory_mb = vm_config.get("memory_mb", 1024)
        cpus = vm_config.get("cpus", 1)

        # Map template to Docker image
        template_map = {
            "ubuntu/focal64": "ubuntu:20.04",
            "ubuntu/jammy64": "ubuntu:22.04",
            "ubuntu/bionic64": "ubuntu:18.04",
        }
        docker_image = template_map.get(template, template)

        # Create container configuration
        container_config = {
            "image": docker_image,
            "name": f"turdparty_vm_{vm_name}",
            "detach": True,
            "tty": True,
            "stdin_open": True,
            "mem_limit": f"{memory_mb}m",
            "cpu_count": cpus,
            "labels": {
                "turdparty.vm": "true",
                "turdparty.vm.name": vm_name,
                "turdparty.vm.template": template
            },
            "environment": {
                "TURDPARTY_VM": "true",
                "TURDPARTY_VM_NAME": vm_name
            },
            "volumes": {
                "/tmp": {"bind": "/tmp", "mode": "rw"}
            },
            "command": "/bin/bash -c 'while true; do sleep 30; done'"
        }

        # Pull image if needed
        try:
            client.images.get(docker_image)
        except docker.errors.ImageNotFound:
            logger.info(f"Pulling Docker image: {docker_image}")
            client.images.pull(docker_image)

        # Create and start container
        container = client.containers.run(**container_config)

        # Get container info
        container.reload()

        # Get container IP
        ip_address = None
        try:
            networks = container.attrs["NetworkSettings"]["Networks"]
            for network_name, network_info in networks.items():
                if network_info.get("IPAddress"):
                    ip_address = network_info["IPAddress"]
                    break
        except Exception:
            pass

        return {
            "success": True,
            "vm_id": container.id,
            "container_name": container.name,
            "ip_address": ip_address,
            "status": container.status,
            "image": docker_image,
            "message": "Docker VM created successfully"
        }

    except Exception as e:
        logger.error(f"Failed to create Docker VM: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": "Docker VM creation failed"
        }


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.start_vm")
def start_vm(self, vm_id: str):
    """Start a VM instance."""
    try:
        logger.info(f"Starting VM: {vm_id}")

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": "running", "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} started successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM started successfully"
        }

    except Exception as e:
        logger.error(f"VM start task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.stop_vm")
def stop_vm(self, vm_id: str, force: bool = False):
    """Stop a VM instance."""
    try:
        logger.info(f"Stopping VM: {vm_id} (force: {force})")

        # Update status to terminating
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status WHERE id = :vm_id"),
                {"status": "terminating", "vm_id": vm_id}
            )
            db.commit()

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status, terminated_at = :terminated_at WHERE id = :vm_id"),
                {"status": "terminated", "terminated_at": datetime.utcnow(), "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} stopped successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM stopped successfully"
        }

    except Exception as e:
        logger.error(f"VM stop task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.delete_vm")
def delete_vm(self, vm_id: str, force: bool = False):
    """Delete a VM instance and clean up resources."""
    try:
        logger.info(f"Deleting VM: {vm_id} (force: {force})")

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET status = :status, terminated_at = :terminated_at WHERE id = :vm_id"),
                {"status": "terminated", "terminated_at": datetime.utcnow(), "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"VM {vm_id} deleted successfully")

        return {
            "vm_id": vm_id,
            "success": True,
            "message": "VM deleted successfully"
        }

    except Exception as e:
        logger.error(f"VM deletion task failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.terminate_vm")
def terminate_vm(self, vm_id: str, vm_type: str):
    """Automatically terminate a VM after 30 minutes runtime."""
    try:
        logger.info(f"Auto-terminating VM: {vm_id}")

        # Stop the VM
        stop_result = stop_vm.delay(vm_id, force=True)

        logger.info(f"VM {vm_id} auto-termination initiated")

        return {
            "vm_id": vm_id,
            "action": "auto_terminate",
            "stop_task_id": stop_result.id
        }

    except Exception as e:
        logger.error(f"VM auto-termination failed: {e}")
        raise


@celery_app.task(bind=True, name="services.workers.tasks.vm_management.inject_file")
def inject_file(self, vm_id: str, file_path: str, injection_path: str):
    """Inject a file into a VM instance."""
    try:
        logger.info(f"Injecting file into VM {vm_id}: {file_path} -> {injection_path}")

        # Simple implementation - just update status for now
        with SessionLocal() as db:
            db.execute(
                text("UPDATE vm_instances SET injection_completed = :completed, injection_path = :path WHERE id = :vm_id"),
                {"completed": True, "path": injection_path, "vm_id": vm_id}
            )
            db.commit()

        logger.info(f"File injection completed for VM {vm_id}")

        return {
            "vm_id": vm_id,
            "file_path": file_path,
            "injection_path": injection_path,
            "success": True
        }

    except Exception as e:
        logger.error(f"File injection failed: {e}")
        raise
