<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tab Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .tab-button { padding: 10px 20px; margin: 5px; border: 1px solid #ccc; background: #f0f0f0; cursor: pointer; }
        .tab-button.active { background: #007cba; color: white; }
        .tab-content { display: none; padding: 20px; border: 1px solid #ccc; margin-top: 10px; }
        .tab-content.active { display: block; }
        .debug-info { background: #f9f9f9; padding: 10px; margin: 10px 0; border-left: 4px solid #007cba; }
    </style>
</head>
<body>
    <h1>💩🎉 Tab Debug Test</h1>
    
    <div class="debug-info">
        <h3>Debug Information:</h3>
        <div id="debug-output"></div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-container">
        <button class="tab-button active" data-tab="architecture">🏗️ Architecture</button>
        <button class="tab-button" data-tab="services">🚀 Service Status</button>
        <button class="tab-button" data-tab="metrics">📊 Metrics</button>
        <button class="tab-button" data-tab="activity">📈 Activity</button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content active" id="architecture-tab">
        <h2>Architecture Tab Content</h2>
        <p>This is the architecture tab content.</p>
    </div>

    <div class="tab-content" id="services-tab">
        <h2>Services Tab Content</h2>
        <p>This is the services tab content.</p>
    </div>

    <div class="tab-content" id="metrics-tab">
        <h2>Metrics Tab Content</h2>
        <p>This is the metrics tab content.</p>
    </div>

    <div class="tab-content" id="activity-tab">
        <h2>Activity Tab Content</h2>
        <p>This is the activity tab content.</p>
    </div>

    <script>
        function debugLog(message) {
            const debugOutput = document.getElementById('debug-output');
            const timestamp = new Date().toLocaleTimeString();
            debugOutput.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            console.log(`[DEBUG] ${message}`);
        }

        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            debugLog(`Found ${tabButtons.length} tab buttons and ${tabContents.length} tab contents`);

            if (tabButtons.length === 0) {
                debugLog('❌ No tab buttons found!');
                return;
            }

            tabButtons.forEach((button, index) => {
                const tabName = button.getAttribute('data-tab');
                debugLog(`Setting up tab button ${index}: ${tabName}`);
                
                button.addEventListener('click', (event) => {
                    event.preventDefault();
                    const targetTab = button.getAttribute('data-tab');
                    
                    debugLog(`🔄 Tab clicked: ${targetTab}`);

                    // Remove active class from all buttons and contents
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active');
                        debugLog(`Removed active from button: ${btn.getAttribute('data-tab')}`);
                    });
                    
                    tabContents.forEach(content => {
                        content.classList.remove('active');
                        debugLog(`Removed active from content: ${content.id}`);
                    });

                    // Add active class to clicked button and corresponding content
                    button.classList.add('active');
                    debugLog(`Added active to button: ${targetTab}`);
                    
                    const targetContent = document.getElementById(`${targetTab}-tab`);
                    
                    if (targetContent) {
                        targetContent.classList.add('active');
                        debugLog(`✅ Activated tab: ${targetTab}`);
                    } else {
                        debugLog(`❌ Target content not found: ${targetTab}-tab`);
                    }
                });
            });

            debugLog('✅ Tab initialization complete');
        }

        // Initialize when DOM is ready
        document.addEventListener('DOMContentLoaded', () => {
            debugLog('DOM loaded, initializing tabs...');
            initializeTabs();
        });

        // Also initialize immediately in case DOM is already loaded
        if (document.readyState === 'loading') {
            debugLog('Document still loading, waiting for DOMContentLoaded...');
        } else {
            debugLog('Document already loaded, initializing tabs immediately...');
            initializeTabs();
        }
    </script>
</body>
</html>
