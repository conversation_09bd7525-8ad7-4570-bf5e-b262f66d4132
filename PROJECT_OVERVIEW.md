# TurdParty - Malware Analysis Platform

A comprehensive malware analysis platform with automated file processing, VM management, and real-time monitoring.

## Quick Start

```bash
# Start core services
docker-compose up -d

# Access the platform
open http://frontend.turdparty.localhost
open http://localhost:8000/docs  # Documentation
open http://kibana.turdparty.localhost  # Analytics
```

## Project Structure

```
├── api/                    # Core API implementation
├── services/              # Microservices (API, workers, frontend)
├── compose/               # Docker Compose configurations
├── docs/                  # Sphinx documentation
├── tests/                 # Comprehensive test suite
├── scripts/               # Utility and deployment scripts
├── frontend/              # React web interface
├── config/                # Configuration files
├── deployment/            # Deployment tools and configs
├── development/           # Development tools and examples
├── documentation/         # Organized project documentation
└── archive/               # Historical files and reports
```

## Key Features

- **Automated Malware Analysis** - Complete file processing pipeline
- **VM Pool Management** - Intelligent VM allocation and lifecycle
- **Real-time Monitoring** - ELK stack integration with live dashboards
- **Modern Web Interface** - React frontend with dark mode
- **Comprehensive Testing** - 21/21 passing tests with multiple frameworks
- **Production Ready** - Docker-based deployment with Traefik integration

## Documentation

- **Main Documentation**: [docs/](docs/) - Sphinx-generated comprehensive docs
- **API Reference**: [docs/getting-started/api-reference.rst](docs/getting-started/api-reference.rst)
- **Testing Guide**: [tests/docs/](tests/docs/)
- **Development Status**: [documentation/status/](documentation/status/)

## Development

```bash
# Install dependencies
nix-shell

# Run tests
./scripts/run-parallel-tests.sh

# Build documentation
./scripts/update-docs.sh

# Start development environment
./dev.sh
```

## Support

- **Issues**: Use GitHub Issues for bug reports and feature requests
- **Documentation**: Comprehensive docs at `/docs` endpoint
- **Testing**: Run `./scripts/run-parallel-tests.sh` for full test suite
