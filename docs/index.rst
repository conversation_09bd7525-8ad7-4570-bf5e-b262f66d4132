TurdParty Malware Analysis Platform Documentation
==================================================

.. image:: _static/logo.svg
   :alt: TurdParty Logo
   :align: center
   :width: 200px

Welcome to the comprehensive documentation for **TurdParty**, a complete malware analysis platform that provides automated file processing, real-time virtual machine monitoring, and advanced threat detection capabilities through a modern microservices architecture.

.. note::
   This documentation covers version |version| of the TurdParty API. For the latest updates and changes, see the :doc:`changelog`.

Quick Start
-----------

.. code-block:: bash

   # Start the complete TurdParty platform
   docker-compose -f compose/docker-compose.yml up -d
   docker-compose -f compose/docker-compose.workers.yml up -d
   docker-compose -f compose/docker-compose.elk.yml up -d

   # Import Kibana dashboards
   ./services/monitoring/elk/kibana/import-dashboards.sh

   # Access the platform
   curl http://localhost:8000/health
   open http://frontend.turdparty.localhost    # React UI
   open http://kibana.turdparty.localhost      # Analytics Dashboards
   open http://flower.turdparty.localhost      # Task Monitoring

Key Features
------------

🚀 **Automated Malware Analysis**
   Complete end-to-end file processing with intelligent VM pool management

🔄 **Workflow Orchestration**
   Advanced task coordination with 5 specialized worker types and automated lifecycle management

🖥️ **Real-time VM Monitoring**
   Comprehensive agent-based monitoring with system metrics, process tracking, and file operations

📊 **ELK Stack Integration**
   Complete data pipeline with Elasticsearch, Logstash, and Kibana for advanced analytics

🎯 **Threat Detection**
   Automated suspicious activity detection with IOC extraction and confidence scoring

📈 **Advanced Visualization**
   Four comprehensive Kibana dashboards for workflow overview, VM monitoring, threat analysis, and system performance

🔧 **Intelligent Pool Management**
   Automatic VM provisioning and maintenance with 2-10 ready VMs at all times

🛡️ **Production-Ready Security**
   Built for real-world malware analysis with proper isolation, monitoring, and error handling

Architecture Overview
---------------------

.. mermaid::

   graph TB
       subgraph "User Interface"
           UI[React Frontend]
           KB[Kibana Dashboards]
       end

       subgraph "API Layer"
           API[FastAPI Server]
       end

       subgraph "Worker Layer"
           WF[Workflow Orchestrator]
           FILE[File Operations]
           VM[VM Management]
           INJ[File Injection]
           POOL[VM Pool Manager]
           ELK[ELK Integration]
           AGENT[Agent Injector]
       end

       subgraph "VM Runtime"
           VM1[VM + Agent]
           VM2[VM + Agent]
           VM3[VM + Agent]
       end

       subgraph "ELK Stack"
           ES[Elasticsearch]
           LS[Logstash]
           KB2[Kibana]
       end

       subgraph "Infrastructure"
           REDIS[Redis Queue]
           DB[PostgreSQL]
           MINIO[MinIO Storage]
       end

       UI --> API
       API --> WF
       WF --> FILE
       WF --> VM
       WF --> INJ
       WF --> ELK
       INJ --> AGENT
       AGENT --> VM1
       AGENT --> VM2
       AGENT --> VM3

       VM1 --> LS
       VM2 --> LS
       VM3 --> LS
       ELK --> LS
       LS --> ES
       ES --> KB
       KB --> KB2

       FILE --> MINIO
       WF --> REDIS
       WF --> DB

API Endpoints Overview
---------------------

.. list-table:: **File Processing API**
   :widths: 20 30 50
   :header-rows: 1

   * - Method
     - Endpoint
     - Description
   * - POST
     - ``/api/v1/files/upload``
     - Upload file for malware analysis
   * - GET
     - ``/api/v1/files/{file_id}``
     - Get file upload details and status
   * - GET
     - ``/api/v1/files/{file_id}/download``
     - Download processed file results

.. list-table:: **Workflow Management API**
   :widths: 20 30 50
   :header-rows: 1

   * - Method
     - Endpoint
     - Description
   * - POST
     - ``/api/v1/workflow/start``
     - Start malware analysis workflow
   * - GET
     - ``/api/v1/workflow/{workflow_id}``
     - Get workflow status and progress
   * - GET
     - ``/api/v1/workflow/{workflow_id}/results``
     - Get analysis results and IOCs

.. list-table:: **VM Management API**
   :widths: 20 30 50
   :header-rows: 1

   * - Method
     - Endpoint
     - Description
   * - GET
     - ``/api/v1/vms/``
     - List all virtual machines
   * - POST
     - ``/api/v1/vms/``
     - Create a new virtual machine
   * - GET
     - ``/api/v1/vms/{vm_id}``
     - Get VM details and status
   * - DELETE
     - ``/api/v1/vms/{vm_id}``
     - Terminate a virtual machine
   * - GET
     - ``/api/v1/vms/pool/status``
     - Get VM pool status and metrics

.. list-table:: **System Health API**
   :widths: 20 30 50
   :header-rows: 1

   * - Method
     - Endpoint
     - Description
   * - GET
     - ``/health``
     - Overall system health check
   * - GET
     - ``/api/v1/health/workers``
     - Worker queue health and status
   * - GET
     - ``/api/v1/health/elk``
     - ELK stack connectivity status

Documentation Sections
----------------------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   getting-started/index
   installation
   quickstart
   configuration
   authentication

.. toctree::
   :maxdepth: 2
   :caption: Platform Overview

   platform/architecture
   platform/workflow
   platform/components
   platform/security

.. toctree::
   :maxdepth: 2
   :caption: Technical Specifications (PRD)

   PRD/index

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/overview
   api/file_processing
   api/workflow_management
   api/vm_management
   api/models
   api/errors

.. toctree::
   :maxdepth: 2
   :caption: Worker Services

   workers/overview
   workers/file_operations
   workers/vm_management
   workers/workflow_orchestration
   workers/vm_pool_management
   workers/elk_integration

.. toctree::
   :maxdepth: 2
   :caption: VM Management

   vm/overview
   vm/pool_management
   vm/monitoring_agent
   vm/lifecycle
   vm/templates

.. toctree::
   :maxdepth: 2
   :caption: ELK Integration

   elk/overview
   elk/data_pipeline
   elk/kibana_dashboards
   elk/vm_monitoring
   elk/threat_detection

.. toctree::
   :maxdepth: 2
   :caption: Monitoring & Analytics

   monitoring/overview
   monitoring/real_time_monitoring
   monitoring/threat_detection
   monitoring/performance_metrics
   monitoring/kibana_usage

.. toctree::
   :maxdepth: 2
   :caption: Development

   development/setup
   development/testing
   development/contributing
   development/architecture
   development/worker_development

.. toctree::
   :maxdepth: 2
   :caption: Deployment

   deployment/docker_compose
   deployment/production
   deployment/scaling
   deployment/security
   deployment/elk_stack

.. toctree::
   :maxdepth: 2
   :caption: Reference

   reference/code_reference
   reference/cli
   reference/configuration
   reference/troubleshooting
   changelog
   glossary

Examples
--------

**Create a VM and Monitor Metrics**

.. code-block:: python

   import asyncio
   import httpx
   import websockets
   import json

   async def create_and_monitor_vm():
       # Create VM
       async with httpx.AsyncClient() as client:
           vm_data = {
               'name': 'malware-analysis-vm',
               'template': 'ubuntu:20.04',
               'vm_type': 'docker',
               'memory_mb': 1024,
               'cpus': 2,
               'domain': 'TurdParty'
           }
           
           response = await client.post(
               'http://localhost:8000/api/v1/vms/',
               json=vm_data
           )
           vm_id = response.json()['vm_id']
           print(f"Created VM: {vm_id}")
       
       # Monitor metrics via WebSocket
       uri = f"ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream"
       async with websockets.connect(uri) as websocket:
           for _ in range(10):
               message = await websocket.recv()
               metrics = json.loads(message)
               print(f"CPU: {metrics['cpu_percent']:.1f}%, "
                     f"Memory: {metrics['memory_percent']:.1f}%")

   # Run the example
   asyncio.run(create_and_monitor_vm())

**Execute Commands via WebSocket**

.. code-block:: javascript

   // JavaScript WebSocket example
   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/vm-id/commands/execute');
   
   ws.onopen = function() {
       // Send command
       ws.send(JSON.stringify({
           command: 'ls -la /tmp',
           working_directory: '/tmp'
       }));
   };
   
   ws.onmessage = function(event) {
       const data = JSON.parse(event.data);
       console.log('Command output:', data.output);
       
       if (data.is_complete) {
           console.log('Command completed with exit code:', data.exit_code);
       }
   };

Support and Community
--------------------

- **Documentation**: https://turdparty.readthedocs.io/
- **GitHub Repository**: https://github.com/tenbahtsecurity/turdparty
- **Issue Tracker**: https://github.com/tenbahtsecurity/turdparty/issues
- **Discussions**: https://github.com/tenbahtsecurity/turdparty/discussions

License
-------

This project is licensed under the MIT License. See the :doc:`license` file for details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
