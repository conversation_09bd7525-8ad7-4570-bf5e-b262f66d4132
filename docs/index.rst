TurdParty VM WebSocket API Documentation
=========================================

.. image:: _static/turdparty-logo.png
   :alt: TurdParty Logo
   :align: center
   :width: 200px

Welcome to the comprehensive documentation for the **TurdParty VM WebSocket API**, a modern malware analysis platform that provides real-time virtual machine management, file injection, and monitoring capabilities through REST APIs and WebSocket connections.

.. note::
   This documentation covers version |version| of the TurdParty API. For the latest updates and changes, see the :doc:`changelog`.

Quick Start
-----------

.. code-block:: bash

   # Start the TurdParty system
   docker-compose up -d
   
   # Access the API
   curl http://localhost:8000/health
   
   # View interactive documentation
   open http://localhost:8000/docs

Key Features
------------

🚀 **Real-time VM Management**
   Create, monitor, and control virtual machines for malware analysis

🔌 **WebSocket Streaming**
   Real-time metrics, command execution, and file monitoring

📊 **ECS Logging**
   Comprehensive structured logging for monitoring and analytics

🔧 **Multi-VM Support**
   Docker containers and Vagrant VMs with gRPC communication

🛡️ **Security-First**
   Built for malware analysis with proper isolation and monitoring

📈 **Performance Optimised**
   Concurrent request handling and efficient resource management

Architecture Overview
---------------------

.. mermaid::

   graph TB
       Client[Client Applications] --> API[TurdParty API Server]
       API --> VM[VM Management Service]
       API --> WS[WebSocket Manager]
       API --> ELK[ELK Stack]
       
       VM --> Docker[Docker VMs]
       VM --> Vagrant[Vagrant VMs]
       
       WS --> Metrics[Metrics Stream]
       WS --> Commands[Command Execution]
       WS --> Files[File Operations]
       
       ELK --> ES[Elasticsearch]
       ELK --> LS[Logstash]
       ELK --> KB[Kibana]
       
       Vagrant --> gRPC[gRPC Port 40000]

API Endpoints Overview
---------------------

.. list-table:: **REST API Endpoints**
   :widths: 20 30 50
   :header-rows: 1

   * - Method
     - Endpoint
     - Description
   * - GET
     - ``/health``
     - System health check
   * - GET
     - ``/api/v1/vms/``
     - List all virtual machines
   * - POST
     - ``/api/v1/vms/``
     - Create a new virtual machine
   * - GET
     - ``/api/v1/vms/{vm_id}``
     - Get VM details
   * - DELETE
     - ``/api/v1/vms/{vm_id}``
     - Delete a virtual machine
   * - GET
     - ``/api/v1/vms/templates``
     - List available VM templates

.. list-table:: **WebSocket Endpoints**
   :widths: 40 60
   :header-rows: 1

   * - Endpoint
     - Description
   * - ``/api/v1/vms/{vm_id}/metrics/stream``
     - Real-time VM performance metrics
   * - ``/api/v1/vms/{vm_id}/commands/execute``
     - Interactive command execution
   * - ``/api/v1/vms/{vm_id}/files/upload``
     - File upload with progress tracking
   * - ``/api/v1/vms/{vm_id}/files/watch``
     - File system monitoring

Documentation Sections
----------------------

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   getting-started/index
   installation
   quickstart
   configuration
   authentication

.. toctree::
   :maxdepth: 2
   :caption: API Reference

   api/overview
   api/rest_endpoints
   api/websocket_endpoints
   api/models
   api/errors

.. toctree::
   :maxdepth: 2
   :caption: VM Management

   vm/overview
   vm/creation
   vm/monitoring
   vm/templates
   vm/lifecycle

.. toctree::
   :maxdepth: 2
   :caption: WebSocket Guide

   websocket/overview
   websocket/metrics
   websocket/commands
   websocket/files
   websocket/authentication

.. toctree::
   :maxdepth: 2
   :caption: Integration

   integration/docker
   integration/vagrant
   integration/grpc
   integration/elk_stack

.. toctree::
   :maxdepth: 2
   :caption: Monitoring & Logging

   monitoring/overview
   monitoring/ecs_logging
   monitoring/metrics
   monitoring/alerts
   monitoring/kibana

.. toctree::
   :maxdepth: 2
   :caption: Development

   development/setup
   development/testing
   development/contributing
   development/architecture

.. toctree::
   :maxdepth: 2
   :caption: Deployment

   deployment/docker_compose
   deployment/production
   deployment/scaling
   deployment/security

.. toctree::
   :maxdepth: 2
   :caption: Reference

   reference/code_reference
   reference/cli
   reference/configuration
   reference/troubleshooting
   changelog
   glossary

Examples
--------

**Create a VM and Monitor Metrics**

.. code-block:: python

   import asyncio
   import httpx
   import websockets
   import json

   async def create_and_monitor_vm():
       # Create VM
       async with httpx.AsyncClient() as client:
           vm_data = {
               'name': 'malware-analysis-vm',
               'template': 'ubuntu:20.04',
               'vm_type': 'docker',
               'memory_mb': 1024,
               'cpus': 2,
               'domain': 'TurdParty'
           }
           
           response = await client.post(
               'http://localhost:8000/api/v1/vms/',
               json=vm_data
           )
           vm_id = response.json()['vm_id']
           print(f"Created VM: {vm_id}")
       
       # Monitor metrics via WebSocket
       uri = f"ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream"
       async with websockets.connect(uri) as websocket:
           for _ in range(10):
               message = await websocket.recv()
               metrics = json.loads(message)
               print(f"CPU: {metrics['cpu_percent']:.1f}%, "
                     f"Memory: {metrics['memory_percent']:.1f}%")

   # Run the example
   asyncio.run(create_and_monitor_vm())

**Execute Commands via WebSocket**

.. code-block:: javascript

   // JavaScript WebSocket example
   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/vm-id/commands/execute');
   
   ws.onopen = function() {
       // Send command
       ws.send(JSON.stringify({
           command: 'ls -la /tmp',
           working_directory: '/tmp'
       }));
   };
   
   ws.onmessage = function(event) {
       const data = JSON.parse(event.data);
       console.log('Command output:', data.output);
       
       if (data.is_complete) {
           console.log('Command completed with exit code:', data.exit_code);
       }
   };

Support and Community
--------------------

- **Documentation**: https://turdparty.readthedocs.io/
- **GitHub Repository**: https://github.com/tenbahtsecurity/turdparty
- **Issue Tracker**: https://github.com/tenbahtsecurity/turdparty/issues
- **Discussions**: https://github.com/tenbahtsecurity/turdparty/discussions

License
-------

This project is licensed under the MIT License. See the :doc:`license` file for details.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
