============================
🏗️ System Architecture Overview
============================

This document provides a comprehensive overview of the TurdParty malware analysis platform architecture, designed for technical architects, developers, and system administrators.

.. contents:: Table of Contents
   :local:
   :depth: 3

🎯 Architecture Principles
=========================

Design Philosophy
----------------

TurdParty is built on modern microservices architecture principles:

- **Scalability**: Horizontal scaling of worker services and VM pools
- **Resilience**: Fault-tolerant design with graceful degradation
- **Security**: Defense-in-depth with VM isolation and network segmentation
- **Observability**: Comprehensive monitoring and logging throughout the system
- **Maintainability**: Clean separation of concerns and well-defined interfaces

Core Architectural Patterns
---------------------------

.. mermaid::

   graph TB
       subgraph "Architectural Patterns"
           MS[Microservices Architecture<br/>Independent, scalable services]
           EDA[Event-Driven Architecture<br/>Asynchronous message processing]
           CQRS[CQRS Pattern<br/>Command/Query separation]
           SAGA[Saga Pattern<br/>Distributed transactions]
       end
       
       subgraph "Infrastructure Patterns"
           CONTAINER[Containerization<br/>Docker-based deployment]
           ORCHESTRATION[Container Orchestration<br/>Docker Compose]
           PROXY[Reverse Proxy<br/>Traefik routing]
           STORAGE[Distributed Storage<br/>MinIO object storage]
       end
       
       MS --> EDA
       EDA --> CQRS
       CQRS --> SAGA
       
       CONTAINER --> ORCHESTRATION
       ORCHESTRATION --> PROXY
       PROXY --> STORAGE

🏗️ High-Level Architecture
==========================

System Overview
--------------

.. mermaid::

   graph TB
       subgraph "External Layer"
           USER[Security Analysts<br/>Malware Researchers]
           API_CLIENT[API Clients<br/>Automation Tools]
       end
       
       subgraph "Presentation Layer"
           UI[React Frontend<br/>Web Interface]
           TRAEFIK[Traefik Proxy<br/>Reverse Proxy & Load Balancer]
       end
       
       subgraph "API Layer"
           API[FastAPI Server<br/>REST API & WebSocket]
           AUTH[Authentication<br/>JWT/OAuth2]
       end
       
       subgraph "Business Logic Layer"
           WORKFLOW[Workflow Orchestrator<br/>Celery Workers]
           VM_MGR[VM Pool Manager<br/>Lifecycle Management]
           FILE_MGR[File Manager<br/>Upload/Download/Storage]
           MONITOR[Monitoring Service<br/>Real-time Analytics]
       end
       
       subgraph "Infrastructure Layer"
           DB[PostgreSQL<br/>Metadata & State]
           CACHE[Redis<br/>Cache & Message Queue]
           STORAGE[MinIO<br/>Object Storage]
           ELK[ELK Stack<br/>Logging & Analytics]
       end
       
       subgraph "Execution Layer"
           VM_POOL[VM Pool<br/>2-10 Ready VMs]
           DOCKER_POOL[Docker Pool<br/>Lightweight Analysis]
       end
       
       USER --> UI
       API_CLIENT --> TRAEFIK
       UI --> TRAEFIK
       TRAEFIK --> API
       API --> AUTH
       API --> WORKFLOW
       
       WORKFLOW --> VM_MGR
       WORKFLOW --> FILE_MGR
       WORKFLOW --> MONITOR
       
       VM_MGR --> VM_POOL
       VM_MGR --> DOCKER_POOL
       FILE_MGR --> STORAGE
       MONITOR --> ELK
       
       WORKFLOW --> DB
       WORKFLOW --> CACHE
       VM_POOL --> ELK
       DOCKER_POOL --> ELK

🔄 Data Flow Architecture
========================

Analysis Workflow
-----------------

.. mermaid::

   sequenceDiagram
       participant User
       participant UI as React UI
       participant API as FastAPI
       participant WF as Workflow
       participant VM as VM Manager
       participant POOL as VM Pool
       participant AGENT as VM Agent
       participant ELK as ELK Stack
       participant STORAGE as MinIO
       
       User->>UI: Upload Malware Sample
       UI->>API: POST /api/v1/files/upload
       API->>STORAGE: Store File
       API->>WF: Trigger Analysis Workflow
       
       WF->>VM: Request VM Allocation
       VM->>POOL: Get Ready VM
       POOL->>VM: Allocate VM Instance
       
       WF->>VM: Deploy File to VM
       WF->>AGENT: Install Monitoring Agent
       AGENT->>ELK: Start Real-time Streaming
       
       loop Analysis Period (30 minutes)
           AGENT->>ELK: Stream Metrics
           VM->>ELK: Runtime Data
           ELK->>UI: Real-time Updates
       end
       
       WF->>VM: Terminate VM
       VM->>POOL: Provision Replacement
       WF->>API: Analysis Complete
       API->>UI: Final Results

🏛️ Service Architecture
=======================

Core Services
------------

.. mermaid::

   graph TB
       subgraph "Frontend Services"
           REACT[React Application<br/>Port 3000]
           NGINX[Nginx Server<br/>Static Content]
       end
       
       subgraph "API Services"
           FASTAPI[FastAPI Server<br/>Port 8000]
           WEBSOCKET[WebSocket Handler<br/>Real-time Communication]
       end
       
       subgraph "Worker Services"
           WORKFLOW_W[Workflow Orchestrator<br/>Celery Worker]
           FILE_W[File Operations<br/>Celery Worker]
           VM_W[VM Management<br/>Celery Worker]
           INJECTION_W[File Injection<br/>Celery Worker]
           POOL_W[VM Pool Manager<br/>Celery Worker]
           ELK_W[ELK Integration<br/>Celery Worker]
       end
       
       subgraph "Infrastructure Services"
           POSTGRES[PostgreSQL<br/>Port 5432]
           REDIS[Redis<br/>Port 6379]
           MINIO[MinIO<br/>Port 9000]
           ELASTICSEARCH[Elasticsearch<br/>Port 9200]
           LOGSTASH[Logstash<br/>Port 5044]
           KIBANA[Kibana<br/>Port 5601]
       end
       
       REACT --> NGINX
       NGINX --> FASTAPI
       FASTAPI --> WEBSOCKET
       
       FASTAPI --> WORKFLOW_W
       WORKFLOW_W --> FILE_W
       WORKFLOW_W --> VM_W
       WORKFLOW_W --> INJECTION_W
       WORKFLOW_W --> POOL_W
       WORKFLOW_W --> ELK_W
       
       FILE_W --> MINIO
       VM_W --> POSTGRES
       POOL_W --> REDIS
       ELK_W --> LOGSTASH
       
       LOGSTASH --> ELASTICSEARCH
       ELASTICSEARCH --> KIBANA

🔒 Security Architecture
=======================

Security Layers
---------------

.. mermaid::

   graph TB
       subgraph "Network Security"
           FIREWALL[Network Firewall<br/>Port-based Access Control]
           VPN[VPN Access<br/>Secure Remote Access]
           SEGMENTATION[Network Segmentation<br/>VM Isolation]
       end
       
       subgraph "Application Security"
           AUTH[Authentication<br/>JWT/OAuth2]
           AUTHZ[Authorization<br/>Role-based Access]
           VALIDATION[Input Validation<br/>API Security]
       end
       
       subgraph "Infrastructure Security"
           CONTAINER[Container Security<br/>Docker Isolation]
           SECRETS[Secret Management<br/>Encrypted Storage]
           TLS[TLS Encryption<br/>Data in Transit]
       end
       
       subgraph "VM Security"
           ISOLATION[VM Isolation<br/>Hypervisor Security]
           MONITORING[Security Monitoring<br/>Behavioral Analysis]
           CLEANUP[Secure Cleanup<br/>Data Sanitization]
       end
       
       FIREWALL --> AUTH
       VPN --> AUTHZ
       SEGMENTATION --> VALIDATION
       
       AUTH --> CONTAINER
       AUTHZ --> SECRETS
       VALIDATION --> TLS
       
       CONTAINER --> ISOLATION
       SECRETS --> MONITORING
       TLS --> CLEANUP

📊 Scalability Design
====================

Horizontal Scaling
-----------------

.. mermaid::

   graph TB
       subgraph "Load Balancing"
           LB[Load Balancer<br/>Traefik]
           API1[API Instance 1]
           API2[API Instance 2]
           API3[API Instance N]
       end
       
       subgraph "Worker Scaling"
           QUEUE[Redis Queue]
           W1[Worker Pool 1<br/>2-4 Workers]
           W2[Worker Pool 2<br/>2-4 Workers]
           W3[Worker Pool N<br/>2-4 Workers]
       end
       
       subgraph "VM Scaling"
           VM_POOL1[VM Pool 1<br/>2-10 VMs]
           VM_POOL2[VM Pool 2<br/>2-10 VMs]
           VM_POOL3[VM Pool N<br/>2-10 VMs]
       end
       
       subgraph "Storage Scaling"
           MINIO_CLUSTER[MinIO Cluster<br/>Distributed Storage]
           DB_CLUSTER[PostgreSQL Cluster<br/>Read Replicas]
           REDIS_CLUSTER[Redis Cluster<br/>Sharded Cache]
       end
       
       LB --> API1
       LB --> API2
       LB --> API3
       
       QUEUE --> W1
       QUEUE --> W2
       QUEUE --> W3
       
       W1 --> VM_POOL1
       W2 --> VM_POOL2
       W3 --> VM_POOL3
       
       API1 --> MINIO_CLUSTER
       API2 --> DB_CLUSTER
       API3 --> REDIS_CLUSTER

🎯 Performance Targets
=====================

System Performance Requirements
------------------------------

.. list-table:: Performance Specifications
   :header-rows: 1
   :widths: 30 25 25 20

   * - Component
     - Target Latency
     - Throughput
     - Availability
   * - **API Endpoints**
     - < 200ms (95th percentile)
     - 1000 req/sec
     - 99.9%
   * - **File Upload**
     - < 5s (100MB file)
     - 50 concurrent uploads
     - 99.5%
   * - **VM Allocation**
     - < 30s (cold start)
     - 10 concurrent VMs
     - 99.0%
   * - **WebSocket Streams**
     - < 100ms (real-time)
     - 100 concurrent streams
     - 99.9%
   * - **ELK Ingestion**
     - < 1s (log processing)
     - 10K events/sec
     - 99.5%

🔗 Integration Points
====================

External Integrations
--------------------

- **Traefik Reverse Proxy**: Automatic service discovery and routing
- **ELK Stack**: Centralized logging and analytics
- **Vagrant/VirtualBox**: VM provisioning and management
- **Docker Engine**: Container orchestration and isolation
- **MinIO**: S3-compatible object storage

Internal APIs
------------

- **REST API**: Primary interface for all operations
- **WebSocket API**: Real-time communication and streaming
- **gRPC API**: High-performance VM communication
- **Celery API**: Asynchronous task processing

📚 Related Documentation
========================

For detailed implementation specifications, see:

- :doc:`component-design` - Detailed component specifications
- :doc:`../specifications/api-specification` - Complete API documentation
- :doc:`../implementation/deployment-architecture` - Infrastructure requirements
- :doc:`../operations/monitoring-operations` - Operational procedures
