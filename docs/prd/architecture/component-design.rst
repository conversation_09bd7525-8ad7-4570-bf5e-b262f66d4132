Platform Components
===================

Overview
--------

TurdParty consists of several interconnected components that work together to provide comprehensive malware analysis capabilities. This document provides an overview of each component and how they integrate to form the complete platform.

Core Services
-------------

API Server
~~~~~~~~~~~

**Location**: ``services/api/``

**Purpose**: Central API gateway providing RESTful endpoints for file upload, workflow management, and system monitoring.

**Key Features**:
   - File upload and management endpoints with Blake3 hashing
   - Workflow initiation and status tracking via Celery
   - VM management and monitoring (Docker/Vagrant backends)
   - Real-time WebSocket connections for live updates
   - OpenAPI documentation generation with interactive UI
   - Health monitoring and metrics collection
   - ELK stack integration for comprehensive logging

**Technical Stack**:
   - FastAPI framework with async/await support
   - Pydantic models for request/response validation
   - SQLAlchemy ORM with PostgreSQL database
   - Redis for caching and session management
   - Traefik integration for authentication and routing

**Endpoints Overview**:
   - ``/api/v1/files/*`` - File upload and management
   - ``/api/v1/vms/*`` - Virtual machine operations
   - ``/api/v1/workflows/*`` - Analysis workflow control
   - ``/api/v1/health`` - System health and status
   - ``/ws/*`` - WebSocket connections for real-time data

**Technology Stack**:
   - FastAPI for high-performance async API
   - SQLAlchemy for database ORM
   - Pydantic for data validation
   - WebSocket support for real-time updates

React Frontend
~~~~~~~~~~~~~~

**Location**: ``services/frontend/``

**Purpose**: Modern web interface for file upload, analysis monitoring, and results visualization.

**Key Features**:
   - Drag-and-drop file upload interface
   - Real-time workflow status monitoring
   - Integration with Kibana dashboards
   - Responsive design with dark mode support
   - File analysis results display

**Technology Stack**:
   - React with TypeScript
   - Material-UI component library
   - Real-time updates via WebSocket
   - Responsive design principles

Worker Services
---------------

Workflow Orchestrator
~~~~~~~~~~~~~~~~~~~~~~

**Location**: ``services/workers/tasks/workflow_orchestrator.py``

**Purpose**: Coordinates the complete file processing pipeline from upload to analysis completion.

**Responsibilities**:
   - Task chain coordination and dependency management
   - Error handling and retry logic
   - Status tracking and progress updates
   - Event streaming to ELK stack
   - 30-minute VM lifecycle enforcement

**Queue**: ``workflow_ops`` (concurrency: 2)

File Operations Worker
~~~~~~~~~~~~~~~~~~~~~~

**Location**: ``services/workers/tasks/file_operations.py``

**Purpose**: Handles file download, validation, and metadata processing.

**Responsibilities**:
   - File download from MinIO storage
   - Integrity verification using Blake3 hashing
   - File format validation and metadata extraction
   - Temporary file management and cleanup

**Queue**: ``file_ops`` (concurrency: 2)

VM Management Worker
~~~~~~~~~~~~~~~~~~~~

**Location**: ``services/workers/tasks/vm_management.py``

**Purpose**: Manages virtual machine lifecycle operations.

**Responsibilities**:
   - VM creation and configuration
   - VM termination and cleanup
   - Resource allocation and monitoring
   - Integration with VM pool management

**Queue**: ``vm_ops`` (concurrency: 1)

File Injection Worker
~~~~~~~~~~~~~~~~~~~~~

**Location**: ``services/workers/tasks/injection_tasks.py``

**Purpose**: Transfers files into running VMs for analysis.

**Responsibilities**:
   - File transfer via Docker exec or SSH
   - Permission and path management
   - Injection verification and status tracking
   - Support for multiple VM types

**Queue**: ``injection_ops`` (concurrency: 2)

VM Pool Manager
~~~~~~~~~~~~~~~

**Location**: ``services/workers/tasks/vm_pool_manager.py``

**Purpose**: Maintains a pool of ready VMs for immediate allocation.

**Responsibilities**:
   - Pool size management (2-10 VMs)
   - Automatic VM provisioning and cleanup
   - Template rotation and load balancing
   - Health monitoring and metrics collection

**Queue**: ``pool_ops`` (concurrency: 1)

ELK Integration Worker
~~~~~~~~~~~~~~~~~~~~~~

**Location**: ``services/workers/tasks/elk_integration.py``

**Purpose**: Streams data to the ELK stack for analysis and visualization.

**Responsibilities**:
   - Workflow event streaming
   - VM metrics data formatting
   - ECS-compliant data structure
   - ELK stack health monitoring

**Queue**: ``elk_ops`` (concurrency: 3)

Data Storage
------------

PostgreSQL Database
~~~~~~~~~~~~~~~~~~~

**Purpose**: Primary data store for workflow state, file metadata, and system configuration.

**Schema Components**:
   - File uploads and metadata
   - Workflow jobs and status tracking
   - VM instances and pool management
   - User authentication and sessions

**Key Features**:
   - ACID compliance for data integrity
   - Relationship management between entities
   - Performance optimization with indexes
   - Backup and recovery capabilities

MinIO Object Storage
~~~~~~~~~~~~~~~~~~~~

**Purpose**: Secure file storage with S3-compatible API.

**Key Features**:
   - UUID-based file naming for security
   - File versioning and metadata storage
   - Integration with file operations workers
   - Scalable storage architecture

**Configuration**:
   - Bucket-based organization
   - Access control and permissions
   - Encryption at rest and in transit
   - Performance optimization

Redis Cache
~~~~~~~~~~~

**Purpose**: Task queue backend and caching layer.

**Key Features**:
   - Celery task queue management
   - Worker coordination and load balancing
   - Task result caching
   - Real-time status updates

**Configuration**:
   - Persistent storage for task reliability
   - Memory optimization for performance
   - Clustering support for high availability

Monitoring and Analytics
------------------------

VM Monitoring Agent
~~~~~~~~~~~~~~~~~~~

**Location**: ``services/monitoring/vm-agent/``

**Purpose**: Lightweight agent deployed in VMs for real-time data collection.

**Data Collected**:
   - System metrics (CPU, memory, disk, network)
   - Process monitoring and activity tracking
   - File system events and operations
   - Network connections and traffic

**Key Features**:
   - Minimal resource footprint (<50MB RAM)
   - Real-time streaming to ELK stack
   - Suspicious activity detection
   - ECS-compliant data formatting

ELK Stack
~~~~~~~~~

**Elasticsearch**
   - Time-series data storage and indexing
   - Full-text search capabilities
   - Aggregation and analytics engine
   - Scalable cluster architecture

**Logstash**
   - Data ingestion and transformation
   - Multiple input source support
   - Real-time processing pipeline
   - Output routing and filtering

**Kibana**
   - Interactive data visualization
   - Dashboard creation and management
   - Real-time monitoring capabilities
   - Alert and notification system

Infrastructure Components
-------------------------

Container Orchestration
~~~~~~~~~~~~~~~~~~~~~~~~

**Docker Compose Services**:
   - Core services (API, database, storage)
   - Worker services (5 specialized workers)
   - ELK stack (Elasticsearch, Logstash, Kibana)
   - Monitoring services (Flower, health checks)

**Network Configuration**:
   - Internal service communication
   - Traefik reverse proxy integration
   - External access control
   - Service discovery and load balancing

Reverse Proxy
~~~~~~~~~~~~~

**Traefik Configuration**:
   - Automatic service discovery
   - SSL/TLS termination
   - Load balancing and failover
   - Request routing and filtering

**Domain Mapping**:
   - ``frontend.turdparty.localhost`` - React UI
   - ``kibana.turdparty.localhost`` - Analytics dashboards
   - ``flower.turdparty.localhost`` - Task monitoring
   - ``api.turdparty.localhost`` - API endpoints

Component Integration
---------------------

Data Flow
~~~~~~~~~

.. mermaid::

   graph TB
       subgraph "User Interface"
           UI[React Frontend]
           KB[Kibana Dashboards]
       end
       
       subgraph "API Layer"
           API[FastAPI Server]
       end
       
       subgraph "Processing Layer"
           WF[Workflow Orchestrator]
           WORKERS[Specialized Workers]
       end
       
       subgraph "Storage Layer"
           DB[PostgreSQL]
           MINIO[MinIO Storage]
           REDIS[Redis Cache]
       end
       
       subgraph "Analytics Layer"
           ELK[ELK Stack]
           AGENT[VM Agents]
       end
       
       UI --> API
       API --> WF
       WF --> WORKERS
       WORKERS --> DB
       WORKERS --> MINIO
       WORKERS --> REDIS
       WORKERS --> ELK
       AGENT --> ELK
       ELK --> KB

Communication Patterns
~~~~~~~~~~~~~~~~~~~~~~

**Synchronous Communication**:
   - HTTP REST API calls between services
   - Database queries and transactions
   - File storage operations

**Asynchronous Communication**:
   - Celery task queue for worker coordination
   - WebSocket connections for real-time updates
   - Event streaming to ELK stack

**Data Persistence**:
   - PostgreSQL for structured data
   - MinIO for file storage
   - Redis for caching and queues
   - Elasticsearch for analytics data

Service Dependencies
~~~~~~~~~~~~~~~~~~~~

**Startup Order**:
   1. Infrastructure services (PostgreSQL, Redis, MinIO)
   2. ELK stack (Elasticsearch, Logstash, Kibana)
   3. Core API service
   4. Worker services
   5. Frontend service

**Health Dependencies**:
   - Workers depend on database and cache availability
   - API service requires database connectivity
   - ELK integration requires Logstash connectivity
   - VM agents require ELK stack availability

Scalability Considerations
--------------------------

Horizontal Scaling
~~~~~~~~~~~~~~~~~~

**Worker Scaling**:
   - Independent scaling of each worker type
   - Queue-based load distribution
   - Configurable concurrency per worker
   - Auto-scaling based on queue depth

**Infrastructure Scaling**:
   - Database read replicas for query scaling
   - MinIO distributed storage clusters
   - Redis clustering for high availability
   - ELK stack horizontal scaling

**VM Pool Scaling**:
   - Dynamic pool size adjustment (2-10 VMs)
   - Template-based VM provisioning
   - Load balancing across available VMs
   - Automatic replacement of terminated VMs

Performance Optimization
~~~~~~~~~~~~~~~~~~~~~~~~

**Resource Management**:
   - Efficient memory allocation and cleanup
   - CPU optimization for concurrent processing
   - Disk I/O optimization for file operations
   - Network bandwidth management

**Caching Strategies**:
   - Redis caching for frequently accessed data
   - Database query optimization and indexing
   - File metadata caching
   - ELK data aggregation optimization

Security Architecture
---------------------

Network Security
~~~~~~~~~~~~~~~~~

**Service Isolation**:
   - Container-based isolation
   - Network segmentation
   - Internal communication encryption
   - Access control and authentication

**External Access**:
   - Traefik reverse proxy for secure access
   - SSL/TLS encryption for all external traffic
   - Rate limiting and DDoS protection
   - API authentication and authorization

Data Security
~~~~~~~~~~~~~

**Encryption**:
   - Data encryption at rest (MinIO, database)
   - Data encryption in transit (all communications)
   - Secure key management
   - Certificate management

**Access Control**:
   - Role-based access control (RBAC)
   - API key management
   - Service-to-service authentication
   - Audit logging for all operations

Operational Excellence
----------------------

Monitoring and Alerting
~~~~~~~~~~~~~~~~~~~~~~~~

**Health Monitoring**:
   - Service health checks and status monitoring
   - Resource utilization tracking
   - Performance metrics collection
   - Error rate monitoring and alerting

**Observability**:
   - Comprehensive logging across all services
   - Distributed tracing for request flows
   - Metrics collection and visualization
   - Real-time dashboards and alerts

Backup and Recovery
~~~~~~~~~~~~~~~~~~~

**Data Backup**:
   - Automated database backups
   - File storage replication
   - Configuration backup and versioning
   - Disaster recovery procedures

**Service Recovery**:
   - Automatic service restart on failure
   - Health check-based recovery
   - Graceful degradation strategies
   - Manual recovery procedures

Maintenance and Updates
~~~~~~~~~~~~~~~~~~~~~~~

**Deployment**:
   - Rolling updates with zero downtime
   - Blue-green deployment strategies
   - Database migration procedures
   - Configuration management

**Monitoring**:
   - Update success monitoring
   - Performance impact assessment
   - Rollback procedures
   - Post-deployment validation
