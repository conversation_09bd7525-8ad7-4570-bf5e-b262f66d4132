WebSocket Overview
==================

The TurdParty API provides real-time communication capabilities through WebSocket connections, enabling live monitoring, command execution, and file operations on virtual machines.

Connection Basics
-----------------

**Base WebSocket URL:**

.. code-block:: text

   ws://localhost:8000/api/v1/vms/{vm_id}/

**Secure WebSocket URL (Production):**

.. code-block:: text

   wss://api.turdparty.localhost/api/v1/vms/{vm_id}/

Connection Lifecycle
--------------------

1. **Connection Establishment**
   
   .. code-block:: javascript

      const ws = new WebSocket('ws://localhost:8000/api/v1/vms/vm-123/metrics/stream');
      
      ws.onopen = function(event) {
          console.log('WebSocket connected');
      };

2. **Message Exchange**
   
   .. code-block:: javascript

      ws.onmessage = function(event) {
          const data = JSON.parse(event.data);
          console.log('Received:', data);
      };
      
      // Send message
      ws.send(JSON.stringify({
          type: 'command',
          data: { command: 'ls -la' }
      }));

3. **Connection Termination**
   
   .. code-block:: javascript

      ws.onclose = function(event) {
          console.log('WebSocket closed:', event.code, event.reason);
      };
      
      ws.onerror = function(error) {
          console.error('WebSocket error:', error);
      };

Available WebSocket Endpoints
-----------------------------

.. list-table::
   :widths: 40 60
   :header-rows: 1

   * - Endpoint
     - Description
   * - ``/metrics/stream``
     - Real-time VM performance metrics
   * - ``/commands/execute``
     - Interactive command execution
   * - ``/files/upload``
     - File upload with progress tracking
   * - ``/files/watch``
     - File system change monitoring
   * - ``/logs/stream``
     - Real-time log streaming
   * - ``/events/subscribe``
     - VM event notifications

Message Format
--------------

All WebSocket messages follow a consistent JSON format:

.. code-block:: json

   {
       "type": "message_type",
       "timestamp": "2025-06-11T07:30:00Z",
       "vm_id": "vm-123",
       "connection_id": "conn-456",
       "data": {
           // Message-specific data
       },
       "metadata": {
           "sequence": 1,
           "source": "vm_metrics_service"
       }
   }

**Message Types:**

.. list-table::
   :widths: 25 75
   :header-rows: 1

   * - Type
     - Description
   * - ``metrics_data``
     - VM performance metrics
   * - ``command_output``
     - Command execution output
   * - ``command_complete``
     - Command execution finished
   * - ``file_progress``
     - File upload/download progress
   * - ``file_complete``
     - File operation completed
   * - ``log_entry``
     - Log message
   * - ``event_notification``
     - VM state change event
   * - ``error``
     - Error message
   * - ``heartbeat``
     - Connection keep-alive

Connection Parameters
---------------------

WebSocket connections accept query parameters for configuration:

.. code-block:: text

   ws://localhost:8000/api/v1/vms/vm-123/metrics/stream?interval=1000&format=json

**Common Parameters:**

.. list-table::
   :widths: 20 20 60
   :header-rows: 1

   * - Parameter
     - Type
     - Description
   * - ``interval``
     - integer
     - Update interval in milliseconds (default: 1000)
   * - ``format``
     - string
     - Data format (json, msgpack)
   * - ``compression``
     - string
     - Compression method (gzip, deflate)
   * - ``buffer_size``
     - integer
     - Buffer size for streaming data
   * - ``timeout``
     - integer
     - Connection timeout in seconds

Error Handling
--------------

WebSocket errors are communicated through error messages:

.. code-block:: json

   {
       "type": "error",
       "timestamp": "2025-06-11T07:30:00Z",
       "error": {
           "code": "VM_NOT_FOUND",
           "message": "Virtual machine 'vm-123' not found",
           "details": {
               "vm_id": "vm-123",
               "connection_id": "conn-456"
           }
       }
   }

**Error Codes:**

.. list-table::
   :widths: 25 75
   :header-rows: 1

   * - Code
     - Description
   * - ``VM_NOT_FOUND``
     - Specified VM does not exist
   * - ``VM_NOT_RUNNING``
     - VM is not in running state
   * - ``PERMISSION_DENIED``
     - Insufficient permissions
   * - ``CONNECTION_LIMIT``
     - Too many concurrent connections
   * - ``INVALID_MESSAGE``
     - Malformed message received
   * - ``TIMEOUT``
     - Operation timed out
   * - ``INTERNAL_ERROR``
     - Server-side error

Connection Management
---------------------

**Connection Limits:**

- Maximum 10 concurrent WebSocket connections per VM
- Maximum 50 concurrent connections per client IP
- Connection timeout: 5 minutes of inactivity

**Heartbeat Mechanism:**

.. code-block:: json

   {
       "type": "heartbeat",
       "timestamp": "2025-06-11T07:30:00Z",
       "connection_id": "conn-456"
   }

Clients should respond to heartbeat messages to maintain the connection.

**Reconnection Strategy:**

.. code-block:: javascript

   class WebSocketManager {
       constructor(url) {
           this.url = url;
           this.reconnectAttempts = 0;
           this.maxReconnectAttempts = 5;
           this.reconnectDelay = 1000;
       }
       
       connect() {
           this.ws = new WebSocket(this.url);
           
           this.ws.onopen = () => {
               this.reconnectAttempts = 0;
               console.log('Connected');
           };
           
           this.ws.onclose = (event) => {
               if (this.reconnectAttempts < this.maxReconnectAttempts) {
                   setTimeout(() => {
                       this.reconnectAttempts++;
                       this.connect();
                   }, this.reconnectDelay * this.reconnectAttempts);
               }
           };
       }
   }

Performance Optimisation
------------------------

**Message Batching:**

For high-frequency data, messages can be batched:

.. code-block:: json

   {
       "type": "batch",
       "timestamp": "2025-06-11T07:30:00Z",
       "messages": [
           {
               "type": "metrics_data",
               "data": { "cpu_percent": 25.5 }
           },
           {
               "type": "metrics_data", 
               "data": { "cpu_percent": 26.1 }
           }
       ]
   }

**Compression:**

Enable compression for large messages:

.. code-block:: text

   ws://localhost:8000/api/v1/vms/vm-123/metrics/stream?compression=gzip

**Filtering:**

Subscribe to specific data types:

.. code-block:: text

   ws://localhost:8000/api/v1/vms/vm-123/metrics/stream?filter=cpu,memory

Security Considerations
-----------------------

**Origin Validation:**

WebSocket connections validate the Origin header in production:

.. code-block:: javascript

   // Allowed origins are configured server-side
   const ws = new WebSocket('ws://localhost:8000/api/v1/vms/vm-123/metrics/stream');

**Rate Limiting:**

WebSocket connections are subject to rate limiting:

- Maximum 100 messages per second per connection
- Burst allowance of 200 messages
- Connections exceeding limits are automatically closed

**Data Sanitisation:**

All incoming WebSocket messages are validated and sanitised before processing.

Client Libraries
----------------

**Python Client:**

.. code-block:: python

   import asyncio
   import websockets
   import json

   async def connect_to_vm_metrics(vm_id):
       uri = f"ws://localhost:8000/api/v1/vms/{vm_id}/metrics/stream"
       
       async with websockets.connect(uri) as websocket:
           async for message in websocket:
               data = json.loads(message)
               print(f"CPU: {data['data']['cpu_percent']:.1f}%")

   asyncio.run(connect_to_vm_metrics("vm-123"))

**JavaScript Client:**

.. code-block:: javascript

   class TurdPartyWebSocket {
       constructor(vmId, endpoint) {
           this.vmId = vmId;
           this.endpoint = endpoint;
           this.url = `ws://localhost:8000/api/v1/vms/${vmId}/${endpoint}`;
           this.callbacks = {};
       }
       
       connect() {
           this.ws = new WebSocket(this.url);
           
           this.ws.onmessage = (event) => {
               const data = JSON.parse(event.data);
               const callback = this.callbacks[data.type];
               if (callback) callback(data);
           };
       }
       
       on(messageType, callback) {
           this.callbacks[messageType] = callback;
       }
       
       send(message) {
           this.ws.send(JSON.stringify(message));
       }
   }

   // Usage
   const vmMetrics = new TurdPartyWebSocket('vm-123', 'metrics/stream');
   vmMetrics.on('metrics_data', (data) => {
       console.log('Metrics:', data.data);
   });
   vmMetrics.connect();

Testing WebSocket Connections
-----------------------------

**Using wscat:**

.. code-block:: bash

   # Install wscat
   npm install -g wscat
   
   # Connect to metrics stream
   wscat -c ws://localhost:8000/api/v1/vms/vm-123/metrics/stream
   
   # Send command
   wscat -c ws://localhost:8000/api/v1/vms/vm-123/commands/execute
   > {"command": "ls -la", "working_directory": "/tmp"}

**Using curl (for testing):**

.. code-block:: bash

   curl --include \
        --no-buffer \
        --header "Connection: Upgrade" \
        --header "Upgrade: websocket" \
        --header "Sec-WebSocket-Key: SGVsbG8sIHdvcmxkIQ==" \
        --header "Sec-WebSocket-Version: 13" \
        http://localhost:8000/api/v1/vms/vm-123/metrics/stream
