Installation Guide
==================

This guide covers the installation and setup of the TurdParty VM WebSocket API system for malware analysis.

System Requirements
-------------------

Minimum Requirements
~~~~~~~~~~~~~~~~~~~~

.. list-table::
   :widths: 25 75
   :header-rows: 1

   * - Component
     - Requirement
   * - **Operating System**
     - Linux (Ubuntu 20.04+, CentOS 8+, Debian 11+)
   * - **CPU**
     - 4 cores (8 cores recommended)
   * - **Memory**
     - 8GB RAM (16GB+ recommended)
   * - **Storage**
     - 100GB available space (SSD recommended)
   * - **Network**
     - Internet connection for image downloads

Recommended Requirements
~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table::
   :widths: 25 75
   :header-rows: 1

   * - Component
     - Recommendation
   * - **CPU**
     - 8+ cores with virtualisation support (Intel VT-x/AMD-V)
   * - **Memory**
     - 32GB+ RAM for multiple concurrent VMs
   * - **Storage**
     - 500GB+ NVMe SSD with high IOPS
   * - **Network**
     - Gigabit Ethernet, isolated analysis network

Software Dependencies
---------------------

Core Dependencies
~~~~~~~~~~~~~~~~~

The following software must be installed on the host system:

**Docker and Docker Compose**

.. code-block:: bash

   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # Add user to docker group
   sudo usermod -aG docker $USER
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose

**Vagrant (Optional - for VM support)**

.. code-block:: bash

   # Install Vagrant
   wget -O- https://apt.releases.hashicorp.com/gpg | gpg --dearmor | sudo tee /usr/share/keyrings/hashicorp-archive-keyring.gpg
   echo "deb [signed-by=/usr/share/keyrings/hashicorp-archive-keyring.gpg] https://apt.releases.hashicorp.com $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/hashicorp.list
   sudo apt update && sudo apt install vagrant
   
   # Install VirtualBox provider
   sudo apt install virtualbox virtualbox-ext-pack

**Nix Package Manager (Recommended)**

.. code-block:: bash

   # Install Nix (single-user installation)
   sh <(curl -L https://nixos.org/nix/install) --no-daemon
   
   # Source Nix environment
   . ~/.nix-profile/etc/profile.d/nix.sh

Installation Methods
--------------------

Method 1: Docker Compose (Recommended)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

This is the fastest way to get TurdParty running:

**1. Clone the Repository**

.. code-block:: bash

   git clone https://github.com/tenbahtsecurity/turdparty.git
   cd turdparty

**2. Configure Environment**

.. code-block:: bash

   # Copy example configuration
   cp .env.example .env
   
   # Edit configuration as needed
   nano .env

**3. Start Services**

.. code-block:: bash

   # Start all services
   docker-compose up -d
   
   # Check service status
   docker-compose ps

**4. Verify Installation**

.. code-block:: bash

   # Test API health
   curl http://localhost:8000/health
   
   # Access web interfaces
   open http://localhost:8000/docs          # API documentation
   open http://kibana.turdparty.localhost   # Kibana dashboard

Method 2: Development Setup with Nix
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

For development and customisation:

**1. Clone and Enter Development Environment**

.. code-block:: bash

   git clone https://github.com/tenbahtsecurity/turdparty.git
   cd turdparty
   
   # Enter Nix development shell
   nix-shell

**2. Install Python Dependencies**

.. code-block:: bash

   # Install development dependencies
   pip install -r requirements-dev.txt
   
   # Install pre-commit hooks
   pre-commit install

**3. Configure Services**

.. code-block:: bash

   # Start supporting services (ELK stack)
   docker-compose up -d elasticsearch logstash kibana
   
   # Start API in development mode
   python -m uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

**4. Run Tests**

.. code-block:: bash

   # Run test suite
   ./scripts/run-parallel-tests.sh
   
   # Run specific test categories
   python -m pytest tests/unit/ -v
   python -m pytest tests/integration/ -v

Method 3: Manual Installation
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

For custom deployments:

**1. Install System Dependencies**

.. code-block:: bash

   # Ubuntu/Debian
   sudo apt update
   sudo apt install python3 python3-pip python3-venv git curl
   
   # CentOS/RHEL
   sudo yum install python3 python3-pip git curl

**2. Create Virtual Environment**

.. code-block:: bash

   python3 -m venv turdparty-env
   source turdparty-env/bin/activate

**3. Install TurdParty**

.. code-block:: bash

   git clone https://github.com/tenbahtsecurity/turdparty.git
   cd turdparty
   pip install -r requirements.txt

**4. Configure Database and Services**

.. code-block:: bash

   # Start Elasticsearch
   docker run -d --name elasticsearch \
     -p 9200:9200 \
     -e "discovery.type=single-node" \
     docker.elastic.co/elasticsearch/elasticsearch:8.11.0
   
   # Start Logstash
   docker run -d --name logstash \
     -p 5000:5000 \
     -v $(pwd)/config/logstash:/usr/share/logstash/pipeline \
     docker.elastic.co/logstash/logstash:8.11.0

**5. Start API Server**

.. code-block:: bash

   python -m uvicorn api.main:app --host 0.0.0.0 --port 8000

Configuration
-------------

Environment Variables
~~~~~~~~~~~~~~~~~~~~~

Key configuration options:

.. code-block:: bash

   # API Configuration
   API_HOST=0.0.0.0
   API_PORT=8000
   API_WORKERS=4
   
   # Database Configuration
   ELASTICSEARCH_URL=http://localhost:9200
   LOGSTASH_HOST=localhost
   LOGSTASH_PORT=5000
   
   # VM Configuration
   DOCKER_HOST=unix:///var/run/docker.sock
   VAGRANT_GRPC_HOST=localhost
   VAGRANT_GRPC_PORT=40000
   
   # Security Configuration
   ENABLE_CORS=true
   ALLOWED_ORIGINS=*
   
   # Logging Configuration
   LOG_LEVEL=INFO
   ECS_LOGGING=true

Docker Compose Configuration
~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Example ``docker-compose.yml`` configuration:

.. code-block:: yaml

   version: '3.8'
   
   services:
     api:
       build: .
       ports:
         - "8000:8000"
         - "40000:40000"  # Vagrant gRPC
       environment:
         - ELASTICSEARCH_URL=http://elasticsearch:9200
         - LOGSTASH_HOST=logstash
       volumes:
         - /var/run/docker.sock:/var/run/docker.sock
       networks:
         - turdparty_network
       network_mode: "host"  # For Vagrant gRPC access
   
     elasticsearch:
       image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
       environment:
         - discovery.type=single-node
         - xpack.security.enabled=false
       ports:
         - "9200:9200"
       networks:
         - turdparty_network
   
     kibana:
       image: docker.elastic.co/kibana/kibana:8.11.0
       environment:
         - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
       ports:
         - "5601:5601"
       networks:
         - turdparty_network
   
   networks:
     turdparty_network:
       driver: bridge

Network Configuration
~~~~~~~~~~~~~~~~~~~~~

**Traefik Reverse Proxy (Production)**

.. code-block:: yaml

   # traefik.yml
   api:
     dashboard: true
   
   entryPoints:
     web:
       address: ":80"
     websecure:
       address: ":443"
   
   providers:
     docker:
       exposedByDefault: false
   
   certificatesResolvers:
     letsencrypt:
       acme:
         email: <EMAIL>
         storage: acme.json
         httpChallenge:
           entryPoint: web

**Firewall Configuration**

.. code-block:: bash

   # UFW (Ubuntu)
   sudo ufw allow 22/tcp      # SSH
   sudo ufw allow 80/tcp      # HTTP
   sudo ufw allow 443/tcp     # HTTPS
   sudo ufw allow 8000/tcp    # API (development)
   sudo ufw allow 40000/tcp   # Vagrant gRPC
   sudo ufw enable

Post-Installation Setup
-----------------------

Initial Configuration
~~~~~~~~~~~~~~~~~~~~~

**1. Create Admin User (if authentication enabled)**

.. code-block:: bash

   curl -X POST http://localhost:8000/api/v1/users/ \
     -H "Content-Type: application/json" \
     -d '{
       "username": "admin",
       "email": "<EMAIL>",
       "password": "secure_password",
       "role": "admin"
     }'

**2. Configure VM Templates**

.. code-block:: bash

   # List available templates
   curl http://localhost:8000/api/v1/vms/templates
   
   # Add custom template
   curl -X POST http://localhost:8000/api/v1/vms/templates \
     -H "Content-Type: application/json" \
     -d '{
       "name": "custom-analysis",
       "base_image": "ubuntu:20.04",
       "tools": ["volatility", "yara", "clamav"]
     }'

**3. Test VM Creation**

.. code-block:: bash

   # Create test VM
   curl -X POST http://localhost:8000/api/v1/vms/ \
     -H "Content-Type: application/json" \
     -d '{
       "name": "test-vm",
       "template": "ubuntu:20.04",
       "vm_type": "docker",
       "memory_mb": 1024,
       "cpus": 2,
       "domain": "TurdParty"
     }'

Verification
~~~~~~~~~~~~

**1. Health Checks**

.. code-block:: bash

   # API health
   curl http://localhost:8000/health
   
   # System status
   curl http://localhost:8000/api/v1/status
   
   # Service connectivity
   curl http://localhost:9200/_cluster/health  # Elasticsearch
   curl http://localhost:5601/api/status       # Kibana

**2. WebSocket Connectivity**

.. code-block:: bash

   # Install wscat for testing
   npm install -g wscat
   
   # Test WebSocket connection
   wscat -c ws://localhost:8000/api/v1/vms/test-vm/metrics/stream

**3. Log Verification**

.. code-block:: bash

   # Check API logs
   docker logs turdparty_api
   
   # Check Elasticsearch indices
   curl http://localhost:9200/_cat/indices?v

Troubleshooting
---------------

Common Issues
~~~~~~~~~~~~~

**Docker Permission Issues**

.. code-block:: bash

   # Add user to docker group
   sudo usermod -aG docker $USER
   
   # Restart session or run:
   newgrp docker

**Port Conflicts**

.. code-block:: bash

   # Check port usage
   sudo netstat -tlnp | grep :8000
   
   # Kill conflicting processes
   sudo fuser -k 8000/tcp

**Memory Issues**

.. code-block:: bash

   # Check available memory
   free -h
   
   # Adjust Docker memory limits
   docker system prune -a

**Elasticsearch Issues**

.. code-block:: bash

   # Increase virtual memory
   sudo sysctl -w vm.max_map_count=262144
   
   # Make permanent
   echo 'vm.max_map_count=262144' | sudo tee -a /etc/sysctl.conf

Log Analysis
~~~~~~~~~~~~

**API Logs**

.. code-block:: bash

   # View real-time logs
   docker logs -f turdparty_api
   
   # Search for errors
   docker logs turdparty_api 2>&1 | grep ERROR

**System Logs**

.. code-block:: bash

   # Check system logs
   journalctl -u docker
   
   # Check disk space
   df -h

Getting Help
------------

**Documentation**
- Online Documentation: https://turdparty.readthedocs.io/
- API Reference: http://localhost:8000/docs

**Community Support**
- GitHub Issues: https://github.com/tenbahtsecurity/turdparty/issues
- Discussions: https://github.com/tenbahtsecurity/turdparty/discussions

**Professional Support**
- Commercial Support: <EMAIL>
- Training and Consulting: <EMAIL>

Next Steps
----------

After successful installation:

1. **Read the Quick Start Guide**: :doc:`quickstart`
2. **Explore API Documentation**: :doc:`api/overview`
3. **Set Up Monitoring**: :doc:`monitoring/overview`
4. **Configure Security**: :doc:`deployment/security`
5. **Join the Community**: https://github.com/tenbahtsecurity/turdparty/discussions
