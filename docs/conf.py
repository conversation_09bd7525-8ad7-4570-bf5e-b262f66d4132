# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

import os
import sys
sys.path.insert(0, os.path.abspath('..'))

project = 'TurdParty Malware Analysis Platform'
copyright = '2025, TurdParty Security'
author = 'TurdParty Security Team'
release = '1.0.0'
version = '1.0'

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.autosummary',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx.ext.intersphinx',
    'sphinx.ext.todo',
    'sphinx.ext.coverage',
    'sphinx.ext.ifconfig',
    'sphinx.ext.githubpages',
    'sphinx_copybutton',  # Copy button for code blocks
    'myst_parser',  # Markdown support
    'sphinxcontrib.mermaid',  # Mermaid diagrams
    # 'sphinxcontrib.openapi',  # Disabled for basic build
    # 'sphinxcontrib.httpdomain',  # Disabled for basic build
    # 'sphinxcontrib.websupport',  # Disabled for basic build
]

templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = 'furo'
html_static_path = ['_static']
html_css_files = ['custom.css']

# Furo theme options - Modern, clean theme with built-in dark mode
html_theme_options = {
    "source_repository": "https://github.com/tenbahtsecurity/turdparty/",
    "source_branch": "main",
    "source_directory": "docs/",
    "announcement": None,
    "light_css_variables": {
        "color-brand-primary": "#3498DB",
        "color-brand-content": "#2980B9",
        "color-admonition-background": "transparent",
    },
    "dark_css_variables": {
        "color-brand-primary": "#4A9EFF",
        "color-brand-content": "#58A6FF",
        "color-admonition-background": "transparent",
    },
    "sidebar_hide_name": False,
    "navigation_with_keys": True,
    "top_of_page_button": "edit",
}

# Custom sidebar
html_sidebars = {
    '**': [
        'relations.html',  # needs 'show_related': True theme option to display
        'searchbox.html',
    ]
}

# -- Extension configuration -------------------------------------------------

# Napoleon settings
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = False
napoleon_use_admonition_for_notes = False
napoleon_use_admonition_for_references = False
napoleon_use_ivar = False
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_preprocess_types = False
napoleon_type_aliases = None
napoleon_attr_annotations = True

# Autodoc settings
autodoc_default_options = {
    'members': True,
    'member-order': 'bysource',
    'special-members': '__init__',
    'undoc-members': True,
    'exclude-members': '__weakref__'
}

# Autosummary settings
autosummary_generate = True
autosummary_imported_members = True

# Intersphinx mapping
intersphinx_mapping = {
    'python': ('https://docs.python.org/3/', None),
    'fastapi': ('https://fastapi.tiangolo.com/', None),
    'pydantic': ('https://docs.pydantic.dev/', None),
    'asyncio': ('https://docs.python.org/3/library/asyncio.html', None),
}

# Todo extension
todo_include_todos = True

# Source file parsers
source_suffix = '.rst'



# Master document
master_doc = 'index'

# Language
language = 'en'

# Pygments style for syntax highlighting
pygments_style = 'github-dark'
pygments_dark_style = 'github-dark'

# HTML output options
html_title = f'{project} v{version}'
html_short_title = 'TurdParty Platform'
html_favicon = '_static/favicon.ico'
# html_logo = '_static/logo.png'  # Logo file not available

# Copy button configuration
copybutton_prompt_text = r">>> |\.\.\. |\$ |In \[\d*\]: | {2,5}\.\.\.: | {5,8}: "
copybutton_prompt_is_regexp = True
copybutton_only_copy_prompt_lines = True
copybutton_remove_prompts = True

# MyST parser configuration for Furo
myst_enable_extensions = [
    "colon_fence",
    "deflist",
    "html_admonition",
    "html_image",
    "linkify",
    "replacements",
    "smartquotes",
    "substitution",
    "tasklist",
]

# LaTeX output options
latex_elements = {
    'papersize': 'a4paper',
    'pointsize': '10pt',
    'preamble': '',
    'fncychap': '',
    'printindex': '',
}

latex_documents = [
    (master_doc, 'TurdPartyPlatform.tex', 'TurdParty Malware Analysis Platform Documentation',
     'TurdParty Security Team', 'manual'),
]

# Manual page output
man_pages = [
    (master_doc, 'turdpartyplatform', 'TurdParty Malware Analysis Platform Documentation',
     [author], 1)
]

# Texinfo output
texinfo_documents = [
    (master_doc, 'TurdPartyPlatform', 'TurdParty Malware Analysis Platform Documentation',
     author, 'TurdPartyPlatform', 'Complete malware analysis platform with ELK integration.',
     'Miscellaneous'),
]

# Epub output
epub_title = project
epub_author = author
epub_publisher = author
epub_copyright = copyright

# Custom CSS and JS
def setup(app):
    app.add_css_file('custom.css')

# OpenAPI settings
openapi_spec_url = 'http://localhost:8000/openapi.json'

# HTTP domain settings
http_index_shortname = 'api'
http_index_localname = 'TurdParty API'

# Mermaid configuration
mermaid_output_format = 'raw'
mermaid_init_js = """
mermaid.initialize({
    theme: 'dark',
    themeVariables: {
        primaryColor: '#3498DB',
        primaryTextColor: '#e0e0e0',
        primaryBorderColor: '#444444',
        lineColor: '#666666',
        secondaryColor: '#2ECC71',
        tertiaryColor: '#E74C3C',
        background: '#2d2d2d',
        mainBkg: '#2d2d2d',
        secondBkg: '#3a3a3a',
        tertiaryBkg: '#1a1a1a'
    },
    flowchart: {
        nodeSpacing: 50,
        rankSpacing: 50,
        curve: 'basis'
    },
    sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35
    }
});
"""

# Add custom roles
rst_prolog = """
.. role:: api-endpoint
.. role:: websocket-endpoint
.. role:: http-method
.. role:: status-code
"""
