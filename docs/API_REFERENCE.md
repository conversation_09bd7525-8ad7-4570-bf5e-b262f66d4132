# API Reference Documentation

This document provides comprehensive API reference for the TurdParty system, including the enhanced VM management endpoints.

## 🚀 Base URL

```
http://localhost:8000
```

## 🔐 Authentication

Currently, the API operates without authentication for development. Production deployments should implement proper authentication via Traefik.

## 📋 VM Management API

### Get VM Templates

Retrieve all available VM templates with descriptions and compatibility information.

**Endpoint:** `GET /api/v1/vms/templates`

**Response:**
```json
[
  {
    "value": "ubuntu:20.04",
    "name": "DOCKER_UBUNTU_2004",
    "description": "Docker Ubuntu 20.04 - Containerized Ubuntu",
    "compatible_vm_types": ["docker"],
    "recommended": true
  },
  {
    "value": "ubuntu/focal64",
    "name": "UBUNTU_2004",
    "description": "Ubuntu 20.04 LTS (Focal Fossa) - Recommended for most workloads",
    "compatible_vm_types": ["vagrant", "docker"],
    "recommended": true
  }
]
```

### Create VM

Create a new VM instance with specified configuration.

**Endpoint:** `POST /api/v1/vms/`

**Request Body:**
```json
{
  "name": "analysis-vm-001",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "memory_mb": 1024,
  "cpus": 2,
  "disk_gb": 20,
  "domain": "TurdParty",
  "description": "Malware analysis environment",
  "auto_start": true,
  "provision_script": "#!/bin/bash\necho 'VM provisioned'"
}
```

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "name": "analysis-vm-001",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "memory_mb": 1024,
  "cpus": 2,
  "disk_gb": 20,
  "status": "creating",
  "domain": "TurdParty",
  "runtime_minutes": 0.0,
  "is_expired": false,
  "created_at": "2025-01-10T10:00:00Z",
  "description": "Malware analysis environment"
}
```

**Validation Rules:**
- `name`: Must be unique, alphanumeric with hyphens
- `template`: Must be from available templates
- `vm_type`: Either "docker" or "vagrant"
- `memory_mb`: 256-8192 MB
- `cpus`: 1-8 cores
- `disk_gb`: 5-100 GB
- `domain`: Must be "TurdParty"

### List VMs

Retrieve paginated list of VMs with optional filtering.

**Endpoint:** `GET /api/v1/vms/`

**Query Parameters:**
- `skip`: Number of records to skip (default: 0)
- `limit`: Maximum records to return (default: 100, max: 1000)
- `status`: Filter by VM status (optional)

**Example:** `GET /api/v1/vms/?skip=0&limit=10&status=running`

**Response:**
```json
{
  "vms": [
    {
      "vm_id": "123e4567-e89b-12d3-a456-************",
      "name": "analysis-vm-001",
      "template": "ubuntu:20.04",
      "vm_type": "docker",
      "status": "running",
      "runtime_minutes": 15.5,
      "is_expired": false,
      "created_at": "2025-01-10T10:00:00Z"
    }
  ],
  "total": 42,
  "skip": 0,
  "limit": 10
}
```

### Get VM Details

Retrieve detailed information about a specific VM.

**Endpoint:** `GET /api/v1/vms/{vm_id}`

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "name": "analysis-vm-001",
  "template": "ubuntu:20.04",
  "vm_type": "docker",
  "memory_mb": 1024,
  "cpus": 2,
  "disk_gb": 20,
  "status": "running",
  "domain": "TurdParty",
  "ip_address": "**********",
  "ssh_port": null,
  "runtime_minutes": 15.5,
  "is_expired": false,
  "created_at": "2025-01-10T10:00:00Z",
  "started_at": "2025-01-10T10:01:00Z",
  "terminated_at": null,
  "description": "Malware analysis environment",
  "error_message": null
}
```

### VM Actions

Perform actions on a VM instance using the unified action endpoint.

**Endpoint:** `POST /api/v1/vms/{vm_id}/action`

**Request Body:**
```json
{
  "action": "start",
  "force": false
}
```

**Available Actions:**
- `start`: Start the VM
- `stop`: Stop the VM gracefully
- `restart`: Restart the VM
- `destroy`: Permanently destroy the VM
- `suspend`: Suspend the VM (Vagrant only)
- `resume`: Resume suspended VM (Vagrant only)

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "name": "analysis-vm-001",
  "action": "start",
  "status": "running",
  "task_id": "celery-task-uuid",
  "force": false,
  "message": "VM start queued"
}
```

### Delete VM

Delete a VM instance and clean up all resources.

**Endpoint:** `DELETE /api/v1/vms/{vm_id}`

**Query Parameters:**
- `force`: Force deletion even if VM is running (default: false)

**Example:** `DELETE /api/v1/vms/{vm_id}?force=true`

**Response:**
```json
{
  "vm_id": "123e4567-e89b-12d3-a456-************",
  "message": "VM deletion queued",
  "task_id": "celery-task-uuid"
}
```

## 📁 File Management API

### Upload File

Upload a file to MinIO storage for analysis.

**Endpoint:** `POST /api/v1/files/upload`

**Request:** Multipart form data
- `file`: File to upload
- `description`: Optional file description

**Response:**
```json
{
  "file_id": "uuid",
  "filename": "malware.exe",
  "size": 1024000,
  "content_type": "application/octet-stream",
  "blake3_hash": "hash_value",
  "uploaded_at": "2025-01-10T10:00:00Z"
}
```

### List Files

Retrieve paginated list of uploaded files.

**Endpoint:** `GET /api/v1/files/`

**Query Parameters:**
- `skip`: Number of records to skip
- `limit`: Maximum records to return

### Get File Details

Retrieve metadata about a specific file.

**Endpoint:** `GET /api/v1/files/{file_id}`

### Download File

Download a file from storage.

**Endpoint:** `GET /api/v1/files/{file_id}/download`

## 💉 File Injection API

### Inject File into VM

Inject a file into a running VM for analysis.

**Endpoint:** `POST /api/v1/vms/{vm_id}/inject`

**Request Body:**
```json
{
  "file_id": "uuid",
  "injection_path": "/tmp/malware.exe",
  "execute": true,
  "monitor": true
}
```

**Response:**
```json
{
  "vm_id": "uuid",
  "file_id": "uuid",
  "injection_path": "/tmp/malware.exe",
  "task_id": "celery-task-uuid",
  "message": "File injection queued"
}
```

## 📊 Monitoring API

### Get VM Metrics

Retrieve real-time metrics for a VM.

**Endpoint:** `GET /api/v1/vms/{vm_id}/metrics`

**Response:**
```json
{
  "vm_id": "uuid",
  "cpu_percent": 15.2,
  "memory_usage_mb": 256,
  "memory_limit_mb": 1024,
  "network_rx_bytes": 1024000,
  "network_tx_bytes": 512000,
  "disk_usage_gb": 2.5,
  "processes": 42,
  "uptime_seconds": 930
}
```

### Get VM Logs

Retrieve logs from a VM.

**Endpoint:** `GET /api/v1/vms/{vm_id}/logs`

**Query Parameters:**
- `lines`: Number of log lines to return (default: 100)
- `since`: Return logs since timestamp

## 🏥 Health Check API

### System Health

Check overall system health.

**Endpoint:** `GET /health/`

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2025-01-10T10:00:00Z",
  "services": {
    "database": "healthy",
    "redis": "healthy",
    "minio": "healthy",
    "docker": "healthy"
  }
}
```

### API Health

Check API service health.

**Endpoint:** `GET /api/v1/health/`

## 📈 Status Codes

### Success Codes
- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `202 Accepted`: Request accepted for processing

### Client Error Codes
- `400 Bad Request`: Invalid request data
- `404 Not Found`: Resource not found
- `422 Unprocessable Entity`: Validation error

### Server Error Codes
- `500 Internal Server Error`: Server error
- `503 Service Unavailable`: Service temporarily unavailable

## 🔧 Error Response Format

All error responses follow a consistent format:

```json
{
  "detail": "Error description",
  "error_code": "VALIDATION_ERROR",
  "timestamp": "2025-01-10T10:00:00Z",
  "request_id": "uuid"
}
```

## 📚 Interactive Documentation

Access the interactive API documentation at:
- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## 🎯 Examples

### Complete VM Analysis Workflow

```bash
# 1. Get available templates
curl http://localhost:8000/api/v1/vms/templates

# 2. Create analysis VM
VM_RESPONSE=$(curl -s -X POST -H "Content-Type: application/json" \
  -d '{"name":"malware-analysis","template":"ubuntu:20.04","vm_type":"docker","memory_mb":1024,"cpus":2,"domain":"TurdParty"}' \
  http://localhost:8000/api/v1/vms/)

VM_ID=$(echo $VM_RESPONSE | jq -r '.vm_id')

# 3. Wait for VM to be ready
while [ "$(curl -s http://localhost:8000/api/v1/vms/$VM_ID | jq -r '.status')" != "running" ]; do
  echo "Waiting for VM..."
  sleep 5
done

# 4. Upload malware sample
FILE_RESPONSE=$(curl -s -X POST -F "file=@malware.exe" \
  http://localhost:8000/api/v1/files/upload)

FILE_ID=$(echo $FILE_RESPONSE | jq -r '.file_id')

# 5. Inject file into VM
curl -X POST -H "Content-Type: application/json" \
  -d '{"file_id":"'$FILE_ID'","injection_path":"/tmp/malware.exe","execute":true,"monitor":true}' \
  http://localhost:8000/api/v1/vms/$VM_ID/inject

# 6. Monitor for 5 minutes
sleep 300

# 7. Get analysis results
curl http://localhost:8000/api/v1/vms/$VM_ID/logs > analysis_results.log

# 8. Terminate VM
curl -X DELETE http://localhost:8000/api/v1/vms/$VM_ID?force=true
```

This API provides comprehensive VM management capabilities for malware analysis with enterprise-grade features including resource management, monitoring, and automated cleanup.
