Notepad++ Binary Analysis Report
==================================

.. meta::
   :description: Comprehensive analysis of npp.8.5.8.Installer.x64.exe execution in Windows VM environment
   :keywords: notepad++, binary analysis, installation footprint, security assessment

.. raw:: html

   <div class="report-header">
       <div class="report-classification internal">INTERNAL</div>
       <div class="report-metadata">
           <span class="report-id">RPT-d5cc1f03-3041-46f5-8ad9-7af2b270ddbb</span>
           <span class="report-date">Generated: 2025-06-12 10:58:05 UTC</span>
       </div>
   </div>

Executive Summary
-----------------

.. admonition:: 🎯 Analysis Overview
   :class: note

   **Binary**: npp.8.5.8.Installer.x64.exe  
   **Size**: 4.6 MB  
   **Risk Level**: :badge:`LOW,badge-success`  
   **Execution Status**: :badge:`SUCCESS,badge-success`  
   **Total Events**: 28

The notepad++ represents a **legitimate software application** with standard installation behavior. Analysis reveals no malicious indicators, with all activities consistent with expected software installation patterns.

.. grid:: 2 2 2 2
    :gutter: 3

    .. grid-item-card:: 📁 Installation Impact
        :class-card: impact-card

        **15 files** created  
        **10 registry keys** modified  
        **3 processes** spawned  
        **22.9 MB** disk usage

    .. grid-item-card:: ⚡ Runtime Behavior
        :class-card: runtime-card

        **3 processes** spawned  
        **0 network** connections  
        **54.0 seconds** execution time  
        **Exit code 0** (success)

    .. grid-item-card:: 🛡️ Security Assessment
        :class-card: security-card

        **Threat Score**: 0/10  
        **Digital Signature**: Valid  
        **Known Good**: ✅ Yes  
        **False Positive**: None

    .. grid-item-card:: 🔍 Behavioral Patterns
        :class-card: behavior-card

        **Pattern**: Standard Installer  
        **Persistence**: Registry Entries  
        **Privilege Escalation**: None  
        **Anti-Analysis**: None

File Information
----------------

.. list-table:: Binary Metadata
   :header-rows: 1
   :widths: 25 75

   * - Property
     - Value
   * - **Filename**
     - npp.8.5.8.Installer.x64.exe
   * - **File Size**
     - 4,796,432 bytes (4.6 MB)
   * - **File Type**
     - PE32+ executable (GUI) x86-64, for MS Windows
   * - **Blake3 Hash**
     - ``56faf67075acaf186801d5e0a6690b6b5d952e94e13e13991a24844c0b0f3392``
   * - **SHA256 Hash**
     - ``ae8cbe56d5824e71069db350cb5235232ce665ecc64a2261930eb7a0c0d58173``
   * - **MD5 Hash**
     - ``b3bd5b49dc41b07a311ca3ee8ce2ce2d``
   * - **Upload Timestamp**
     - 2025-06-12T07:57:24.581695Z
   * - **Analysis UUID**
     - ``d5cc1f03-3041-46f5-8ad9-7af2b270ddbb``

Installation Footprint Analysis
-------------------------------

Filesystem Changes
~~~~~~~~~~~~~~~~~~

The installer created **15 files** across the Windows filesystem:

.. code-block:: text

   📁 C:\Program Files\Notepad++\
   ├── 📄 notepad++.exe
   ├── 📄 SciLexer.dll
   ├── 📄 langs.xml
   ├── 📄 stylers.xml
   ├── 📄 config.xml
   ├── 📄 shortcuts.xml
   ├── 📄 contextMenu.xml

   📁 C:\Program Files\Notepad++\plugins\
   ├── 📄 DSpellCheck.dll
   ├── 📄 NppConverter.dll
   ├── 📄 mimeTools.dll

   📁 C:\Program Files\Notepad++\themes\
   ├── 📄 DarkModeDefault.xml

   📁 C:\Program Files\Notepad++\autoCompletion\
   ├── 📄 c.xml
   ├── 📄 python.xml

   📁 C:\Program Files\Notepad++\localization\
   ├── 📄 english.xml

   📁 C:\Users\<USER>\Desktop\
   ├── 📄 Notepad++.lnk


Registry Modifications
~~~~~~~~~~~~~~~~~~~~~~

The installer made **10 registry changes**:

.. code-block:: registry

   HKEY_LOCAL_MACHINE\SOFTWARE\Notepad++
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepad++\version="8.5.8"
   HKEY_LOCAL_MACHINE\SOFTWARE\Notepad++\installPath="C:\Program Files\Notepad++"
   HKEY_CURRENT_USER\SOFTWARE\Classes\.txt\OpenWithProgids\Notepad++_file
   HKEY_CURRENT_USER\SOFTWARE\Classes\Applications\notepad++.exe
   HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Notepad++
   HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Notepad++\DisplayName="Notepad++ (64-bit x64)"
   HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Notepad++\DisplayVersion="8.5.8"
   HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Notepad++\Publisher="Notepad++ Team"
   HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\Notepad++\InstallLocation="C:\Program Files\Notepad++\"

Runtime Behavior Analysis
--------------------------

Process Execution Details
~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Process Execution Analysis
   :header-rows: 1
   :widths: 30 15 55

   * - Process Name
     - PID
     - Command Line
   * - npp.8.5.8.Installer.x64.exe
     - 1234
     - ``npp.8.5.8.Installer.x64.exe /S``
   * - msiexec.exe
     - 1567
     - ``msiexec.exe /i notepadpp.msi /quiet``
   * - notepad++.exe
     - 1890
     - ``notepad++.exe``


ECS Data Summary
----------------

.. admonition:: 📊 Elasticsearch Data Collection
   :class: note

   **Total Log Entries**: 28 events  
   **Collection Duration**: 54.0 seconds  
   **Data Sources**: vm-agent, file-monitor, process-monitor, registry-monitor

Event Distribution
~~~~~~~~~~~~~~~~~~

.. raw:: html

   <div class="event-distribution">
       <div class="event-category">
           <h4>📁 File Events (15)</h4>
           <div class="event-bar" style="width: 54%;">54%</div>
       </div>
       <div class="event-category">
           <h4>🔑 Registry Events (10)</h4>
           <div class="event-bar" style="width: 36%;">36%</div>
       </div>
       <div class="event-category">
           <h4>🔄 Process Events (3)</h4>
           <div class="event-bar" style="width: 11%;">11%</div>
       </div>
   </div>

Data Export
-----------

.. tabs::

   .. tab:: 📄 JSON Export

      .. code-block:: bash

         # Download complete report data
         curl "http://api.turdparty.localhost/api/v1/reports/binary/d5cc1f03-3041-46f5-8ad9-7af2b270ddbb" \
           -H "Accept: application/json" > notepad-report.json

   .. tab:: 📊 ECS Data

      .. code-block:: bash

         # Export ECS-compliant event data
         curl "http://elasticsearch.turdparty.localhost/turdparty-*/_search" \
           -H "Content-Type: application/json" \
           -d '{"query": {"term": {"file_uuid.keyword": "d5cc1f03-3041-46f5-8ad9-7af2b270ddbb"}}}'

Conclusion
----------

.. admonition:: ✅ Final Assessment
   :class: tip

   The notepad++ demonstrates **standard, benign behavior** consistent with legitimate software installation. No security concerns were identified during the comprehensive analysis.

   **Recommendations:**
   
   * ✅ **Safe for deployment** in enterprise environments
   * ✅ **No additional security controls** required
   * ✅ **Standard software approval** process applicable

Report Metadata
---------------

.. list-table:: Report Generation Details
   :header-rows: 1
   :widths: 30 70

   * - Property
     - Value
   * - **Report ID**
     - RPT-d5cc1f03-3041-46f5-8ad9-7af2b270ddbb
   * - **Generated At**
     - 2025-06-12T10:58:05Z
   * - **Analysis Engine**
     - TurdParty v1.0.0
   * - **Report Version**
     - 1.0
   * - **Classification**
     - Internal

.. raw:: html

   <div class="report-footer">
       <p><strong>💩🎉 TurdParty Binary Analysis Platform 🎉💩</strong></p>
       <p>Comprehensive Windows Binary Security Analysis</p>
   </div>
