Git Binary Analysis Report
============================

.. meta::
   :description: Comprehensive analysis of Git-2.42.0-64-bit.exe execution in Windows VM environment
   :keywords: git, binary analysis, installation footprint, security assessment

.. raw:: html

   <div class="report-header">
       <div class="report-classification internal">INTERNAL</div>
       <div class="report-metadata">
           <span class="report-id">RPT-a1b2c3d4-e5f6-7890-1234-************</span>
           <span class="report-date">Generated: 2025-06-12 10:57:30 UTC</span>
       </div>
   </div>

Executive Summary
-----------------

.. admonition:: 🎯 Analysis Overview
   :class: note

   **Binary**: Git-2.42.0-64-bit.exe  
   **Size**: 50.0 MB  
   **Risk Level**: :badge:`LOW,badge-success`  
   **Execution Status**: :badge:`SUCCESS,badge-success`  
   **Total Events**: 18

The git represents a **legitimate software application** with standard installation behavior. Analysis reveals no malicious indicators, with all activities consistent with expected software installation patterns.

.. grid:: 2 2 2 2
    :gutter: 3

    .. grid-item-card:: 📁 Installation Impact
        :class-card: impact-card

        **9 files** created  
        **6 registry keys** modified  
        **3 processes** spawned  
        **250.0 MB** disk usage

    .. grid-item-card:: ⚡ Runtime Behavior
        :class-card: runtime-card

        **3 processes** spawned  
        **0 network** connections  
        **51.0 seconds** execution time  
        **Exit code 0** (success)

    .. grid-item-card:: 🛡️ Security Assessment
        :class-card: security-card

        **Threat Score**: 0/10  
        **Digital Signature**: Valid  
        **Known Good**: ✅ Yes  
        **False Positive**: None

    .. grid-item-card:: 🔍 Behavioral Patterns
        :class-card: behavior-card

        **Pattern**: Standard Installer  
        **Persistence**: Registry Entries  
        **Privilege Escalation**: None  
        **Anti-Analysis**: None

File Information
----------------

.. list-table:: Binary Metadata
   :header-rows: 1
   :widths: 25 75

   * - Property
     - Value
   * - **Filename**
     - Git-2.42.0-64-bit.exe
   * - **File Size**
     - 52,428,800 bytes (50.0 MB)
   * - **File Type**
     - PE32+ executable (GUI) x86-64, for MS Windows
   * - **Blake3 Hash**
     - ``e3c2d296d670e643074ce36b3daaadc73e4c5d420d8fa27fd1cf3f977ad574ab``
   * - **SHA256 Hash**
     - ``a8f534c0520625165bff266a1f9ec33fdf8c0dfee4de5beaa1d42e56c7afe4b1``
   * - **MD5 Hash**
     - ``8d1244d5fbd6aaa325ea0353eba4bd1f``
   * - **Upload Timestamp**
     - 2025-06-12T08:50:53.543483Z
   * - **Analysis UUID**
     - ``a1b2c3d4-e5f6-7890-1234-************``

Installation Footprint Analysis
-------------------------------

Filesystem Changes
~~~~~~~~~~~~~~~~~~

The installer created **9 files** across the Windows filesystem:

.. code-block:: text

   📁 C:\Program Files\Git\bin\
   ├── 📄 git.exe
   ├── 📄 bash.exe
   ├── 📄 sh.exe

   📁 C:\Program Files\Git\libexec\git-core\
   ├── 📄 git-add.exe
   ├── 📄 git-commit.exe

   📁 C:\Program Files\Git\etc\
   ├── 📄 gitconfig
   ├── 📄 bash.bashrc

   📁 C:\Users\<USER>\Desktop\
   ├── 📄 Git Bash.lnk
   ├── 📄 Git CMD.lnk


Registry Modifications
~~~~~~~~~~~~~~~~~~~~~~

The installer made **6 registry changes**:

.. code-block:: registry

   HKEY_LOCAL_MACHINE\SOFTWARE\GitForWindows="git_value_0"
   HKEY_LOCAL_MACHINE\SOFTWARE\GitForWindows\InstallPath="git_value_1"
   HKEY_LOCAL_MACHINE\SOFTWARE\GitForWindows\Version="git_value_2"
   HKEY_CURRENT_USER\SOFTWARE\Classes\Directory\shell\git_shell="git_value_3"
   HKEY_CURRENT_USER\SOFTWARE\Classes\Directory\Background\shell\git_shell="git_value_4"
   HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\Session Manager\Environment\Path="git_value_5"

Runtime Behavior Analysis
--------------------------

Process Execution Details
~~~~~~~~~~~~~~~~~~~~~~~~~

.. list-table:: Process Execution Analysis
   :header-rows: 1
   :widths: 30 15 55

   * - Process Name
     - PID
     - Command Line
   * - Git-2.42.0-64-bit.exe
     - 2000
     - ``Git-2.42.0-64-bit.exe /SILENT``
   * - msiexec.exe
     - 2001
     - ``msiexec.exe /SILENT``
   * - git.exe
     - 2002
     - ``git.exe /SILENT``


ECS Data Summary
----------------

.. admonition:: 📊 Elasticsearch Data Collection
   :class: note

   **Total Log Entries**: 18 events  
   **Collection Duration**: 51.0 seconds  
   **Data Sources**: vm-agent, file-monitor, process-monitor, registry-monitor

Event Distribution
~~~~~~~~~~~~~~~~~~

.. raw:: html

   <div class="event-distribution">
       <div class="event-category">
           <h4>📁 File Events (9)</h4>
           <div class="event-bar" style="width: 50%;">50%</div>
       </div>
       <div class="event-category">
           <h4>🔑 Registry Events (6)</h4>
           <div class="event-bar" style="width: 33%;">33%</div>
       </div>
       <div class="event-category">
           <h4>🔄 Process Events (3)</h4>
           <div class="event-bar" style="width: 17%;">17%</div>
       </div>
   </div>

Data Export
-----------

.. tabs::

   .. tab:: 📄 JSON Export

      .. code-block:: bash

         # Download complete report data
         curl "http://api.turdparty.localhost/api/v1/reports/binary/a1b2c3d4-e5f6-7890-1234-************" \
           -H "Accept: application/json" > git-report.json

   .. tab:: 📊 ECS Data

      .. code-block:: bash

         # Export ECS-compliant event data
         curl "http://elasticsearch.turdparty.localhost/turdparty-*/_search" \
           -H "Content-Type: application/json" \
           -d '{"query": {"term": {"file_uuid.keyword": "a1b2c3d4-e5f6-7890-1234-************"}}}'

Conclusion
----------

.. admonition:: ✅ Final Assessment
   :class: tip

   The git demonstrates **standard, benign behavior** consistent with legitimate software installation. No security concerns were identified during the comprehensive analysis.

   **Recommendations:**
   
   * ✅ **Safe for deployment** in enterprise environments
   * ✅ **No additional security controls** required
   * ✅ **Standard software approval** process applicable

Report Metadata
---------------

.. list-table:: Report Generation Details
   :header-rows: 1
   :widths: 30 70

   * - Property
     - Value
   * - **Report ID**
     - RPT-a1b2c3d4-e5f6-7890-1234-************
   * - **Generated At**
     - 2025-06-12T10:57:30Z
   * - **Analysis Engine**
     - TurdParty v1.0.0
   * - **Report Version**
     - 1.0
   * - **Classification**
     - Internal

.. raw:: html

   <div class="report-footer">
       <p><strong>💩🎉 TurdParty Binary Analysis Platform 🎉💩</strong></p>
       <p>Comprehensive Windows Binary Security Analysis</p>
   </div>
