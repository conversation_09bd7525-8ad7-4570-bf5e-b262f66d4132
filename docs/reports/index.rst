💩🎉 TurdParty Binary Analysis Reports Platform 🎉💩
========================================================

.. image:: _static/turdparty-logo.png
   :alt: TurdParty Logo
   :align: center
   :width: 200px

Welcome to the **TurdParty Binary Analysis Reports Platform** - your comprehensive source for Windows binary execution analysis, threat intelligence, and security assessments.

.. note::
   This platform provides automated analysis reports for Windows binaries executed in isolated VM environments with comprehensive ECS data collection and behavioral analysis.

Quick Navigation
----------------

.. grid:: 2 2 2 2
    :gutter: 3

    .. grid-item-card:: 📊 Latest Reports
        :link: latest-reports
        :link-type: ref

        View the most recent binary analysis reports

    .. grid-item-card:: 🔍 Search Reports
        :link: search-reports
        :link-type: ref

        Search and filter reports by various criteria

    .. grid-item-card:: 📈 Threat Intelligence
        :link: threat-intelligence
        :link-type: ref

        Access threat intelligence and security assessments

    .. grid-item-card:: ⚙️ API Documentation
        :link: api-documentation
        :link-type: ref

        Learn how to integrate with the reporting API

Platform Overview
------------------

The TurdParty Reports Platform provides:

* **Automated Binary Analysis**: Complete execution analysis in Windows VMs
* **Installation Footprint Tracking**: Files, registry, services, and network changes
* **Runtime Behavior Analysis**: Process execution, resource usage, and performance metrics
* **Security Assessment**: Threat scoring, behavioral patterns, and risk evaluation
* **Comparative Analysis**: Side-by-side comparison of multiple binaries
* **Executive Summaries**: High-level reports for management and stakeholders

.. _latest-reports:

Latest Reports
--------------

.. toctree::
   :maxdepth: 2
   :caption: Recent Analysis Reports

   reports/notepadpp-analysis\n   reports/git-analysis
   reports/notepad-analysis
   reports/vscode-analysis
   reports/python-installer-analysis
   reports/git-windows-analysis
   reports/nodejs-analysis

.. _search-reports:

Search & Filter Reports
-----------------------

.. raw:: html

   <div class="search-container">
       <h3>🔍 Report Search</h3>
       <form id="report-search" class="search-form">
           <div class="search-row">
               <label for="filename">Filename:</label>
               <input type="text" id="filename" name="filename" placeholder="e.g., notepad++, vscode">
           </div>
           <div class="search-row">
               <label for="risk-level">Risk Level:</label>
               <select id="risk-level" name="risk-level">
                   <option value="">All Levels</option>
                   <option value="low">Low</option>
                   <option value="medium">Medium</option>
                   <option value="high">High</option>
                   <option value="critical">Critical</option>
               </select>
           </div>
           <div class="search-row">
               <label for="date-range">Date Range:</label>
               <input type="date" id="date-from" name="date-from">
               <span>to</span>
               <input type="date" id="date-to" name="date-to">
           </div>
           <div class="search-row">
               <button type="submit" class="search-btn">🔍 Search Reports</button>
               <button type="button" class="clear-btn" onclick="clearSearch()">🗑️ Clear</button>
           </div>
       </form>
   </div>

Report Categories
-----------------

.. _malware-analysis:

Malware Analysis Reports
~~~~~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 1
   :caption: Malware Analysis

   categories/malware/index

.. _software-analysis:

Software Analysis Reports
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. toctree::
   :maxdepth: 1
   :caption: Software Analysis

   categories/software/index

.. _threat-intelligence:

Threat Intelligence
-------------------

.. toctree::
   :maxdepth: 2
   :caption: Threat Intelligence

   intelligence/threat-landscape
   intelligence/behavioral-patterns
   intelligence/attack-vectors
   intelligence/indicators-of-compromise

Executive Dashboard
-------------------

.. raw:: html

   <div class="dashboard-container">
       <div class="dashboard-grid">
           <div class="dashboard-card">
               <h3>📊 Reports Generated</h3>
               <div class="metric-value" id="total-reports">1,247</div>
               <div class="metric-label">Total Reports</div>
           </div>
           <div class="dashboard-card">
               <h3>🔍 Binaries Analyzed</h3>
               <div class="metric-value" id="binaries-analyzed">892</div>
               <div class="metric-label">Unique Binaries</div>
           </div>
           <div class="dashboard-card">
               <h3>⚠️ Threats Detected</h3>
               <div class="metric-value" id="threats-detected">23</div>
               <div class="metric-label">High Risk</div>
           </div>
           <div class="dashboard-card">
               <h3>🖥️ VM Executions</h3>
               <div class="metric-value" id="vm-executions">1,156</div>
               <div class="metric-label">Total Executions</div>
           </div>
       </div>
   </div>

Recent Activity
---------------

.. list-table:: Recent Analysis Activity
   :header-rows: 1
   :widths: 20 30 15 15 20

   * - Timestamp
     - Binary
     - Risk Level
     - Status
     - Actions
   * - 2025-06-12 09:57
     - npp.8.5.8.Installer.x64.exe
     - Low
     - ✅ Complete
     - `View Report <reports/notepadpp-analysis.html>`_
   * - 2025-06-12 09:45
     - VSCodeSetup-x64.exe
     - Low
     - ✅ Complete
     - `View Report <reports/vscode-analysis.html>`_
   * - 2025-06-12 09:30
     - python-3.12.1-amd64.exe
     - Low
     - ✅ Complete
     - `View Report <reports/python-installer-analysis.html>`_
   * - 2025-06-12 09:15
     - Git-2.42.0-64-bit.exe
     - Low
     - ✅ Complete
     - `View Report <reports/git-windows-analysis.html>`_
   * - 2025-06-12 09:00
     - node-v20.10.0-x64.msi
     - Low
     - ✅ Complete
     - `View Report <reports/nodejs-analysis.html>`_

.. _api-documentation:

API Integration
---------------

.. toctree::
   :maxdepth: 2
   :caption: API Documentation

   api/overview
   api/authentication
   api/endpoints
   api/examples
   api/sdk

System Architecture
-------------------

.. mermaid::

   graph TB
       A[Binary Upload] --> B[VM Allocation]
       B --> C[File Injection]
       C --> D[Installation Execution]
       D --> E[Behavior Monitoring]
       E --> F[ECS Data Collection]
       F --> G[Report Generation]
       G --> H[Sphinx Platform]
       
       subgraph "Data Sources"
           I[Elasticsearch]
           J[MinIO Storage]
           K[VM Metrics]
       end
       
       F --> I
       F --> J
       E --> K
       
       subgraph "Report Types"
           L[Executive Summary]
           M[Technical Analysis]
           N[Threat Assessment]
           O[Comparative Analysis]
       end
       
       H --> L
       H --> M
       H --> N
       H --> O

Platform Features
------------------

.. tabs::

   .. tab:: 🔍 Analysis Capabilities

      * **Installation Footprint Analysis**
        
        - Files and directories created/modified
        - Registry key changes and startup entries
        - Services installed and configured
        - Network configuration changes

      * **Runtime Behavior Monitoring**
        
        - Process execution and command lines
        - Network connections and DNS queries
        - Resource usage (CPU, memory, disk I/O)
        - Performance metrics and timing

   .. tab:: 🛡️ Security Assessment

      * **Threat Intelligence**
        
        - Behavioral pattern recognition
        - Known malware family identification
        - Attack vector analysis
        - Indicators of Compromise (IoCs)

      * **Risk Scoring**
        
        - Automated threat scoring (0-10 scale)
        - Risk level classification
        - Security recommendation engine
        - False positive reduction

   .. tab:: 📊 Reporting Features

      * **Multiple Report Formats**
        
        - Interactive HTML reports
        - PDF exports for sharing
        - JSON data for API integration
        - Executive summary dashboards

      * **Advanced Analytics**
        
        - Comparative analysis tools
        - Trend analysis and patterns
        - Historical data correlation
        - Custom report generation

   .. tab:: 🔧 Platform Integration

      * **API Integration**
        
        - RESTful API endpoints
        - Real-time data streaming
        - Webhook notifications
        - SDK libraries

      * **Data Export**
        
        - ECS-compliant data format
        - STIX/TAXII threat intelligence
        - MITRE ATT&CK mapping
        - Custom data formats

Quick Start Guide
-----------------

1. **Upload Binary**: Submit Windows executable for analysis
2. **Monitor Execution**: Track VM execution progress in real-time
3. **Review Report**: Access comprehensive analysis results
4. **Export Data**: Download reports in preferred format
5. **Integrate**: Use API endpoints for automation

.. code-block:: bash

   # Quick API example
   curl -X POST "http://api.turdparty.localhost/api/v1/files/upload" \
     -F "file=@suspicious.exe" \
     -F "description=Suspicious executable analysis"

   # Get report
   curl "http://api.turdparty.localhost/api/v1/reports/binary/{uuid}"

Support & Documentation
------------------------

.. toctree::
   :maxdepth: 1
   :caption: Support

   support/faq
   support/troubleshooting
   support/contact
   support/changelog

.. note::
   For technical support or questions about the TurdParty platform, please contact the security research team or submit an issue through the appropriate channels.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
