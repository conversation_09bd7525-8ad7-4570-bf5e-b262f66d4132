====================
🚀 Installation Guide
====================

This guide walks you through setting up the complete TurdParty malware analysis platform on your local environment.

.. contents:: Table of Contents
   :local:
   :depth: 2

📋 Pre-Installation Checklist
=============================

Before starting, ensure you have completed the :doc:`prerequisites`:

.. admonition:: Prerequisites Check
   :class: note

   ✅ **System Requirements**: 16GB+ RAM, 8+ CPU cores, 500GB+ storage  
   ✅ **Docker & Docker Compose**: Latest versions installed  
   ✅ **Vagrant & VirtualBox**: For full VM analysis (optional)  
   ✅ **Git**: For repository cloning  
   ✅ **Network Configuration**: Local domain routing configured  

🔄 Installation Process
=======================

Installation Flow
----------------

.. mermaid::

   graph TB
       START[Start Installation] --> CLONE[Clone Repository]
       CLONE --> ENV[Configure Environment]
       ENV --> NETWORK[Setup Networks]
       NETWORK --> SERVICES[Start Services]
       SERVICES --> VERIFY[Verify Installation]
       VERIFY --> COMPLETE[Installation Complete]
       
       ENV --> ENV_FILE[Create .env file]
       ENV --> SECRETS[Generate secrets]
       
       NETWORK --> DOCKER_NET[Create Docker networks]
       NETWORK --> HOSTS[Configure /etc/hosts]
       
       SERVICES --> CORE[Core services]
       SERVICES --> STORAGE[Storage services]
       SERVICES --> MONITORING[Monitoring stack]
       
       VERIFY --> HEALTH[Health checks]
       VERIFY --> UI[Web interfaces]
       VERIFY --> API[API endpoints]

Step 1: Clone Repository
-----------------------

.. code-block:: bash

   # Clone the TurdParty repository
   git clone https://github.com/tenbahtsecurity/turdparty.git
   cd turdparty
   
   # Switch to development branch (if needed)
   git checkout develop/local

Step 2: Environment Configuration
---------------------------------

.. code-block:: bash

   # Copy environment template
   cp .env.example .env
   
   # Generate secure passwords
   ./scripts/generate-secrets.sh
   
   # Edit configuration (optional)
   nano .env

**Key Environment Variables:**

.. code-block:: bash

   # Core Configuration
   COMPOSE_PROJECT_NAME=turdpartycollab
   TURDPARTY_DOMAIN=turdparty.localhost
   
   # Database Configuration
   POSTGRES_PASSWORD=your_secure_password
   POSTGRES_DB=turdparty
   POSTGRES_USER=turdparty
   
   # MinIO Configuration
   MINIO_ROOT_USER=turdparty
   MINIO_ROOT_PASSWORD=your_secure_password
   
   # Redis Configuration
   REDIS_PASSWORD=your_secure_password
   
   # Elasticsearch Configuration
   ELASTIC_PASSWORD=your_secure_password

Step 3: Network Setup
---------------------

.. code-block:: bash

   # Create Docker networks
   docker network create turdpartycollab_net
   docker network create traefik_network
   
   # Configure local DNS (add to /etc/hosts)
   echo "127.0.0.1 frontend.turdparty.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 status.turdparty.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 kibana.turdparty.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 flower.turdparty.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 elasticsearch.turdparty.localhost" | sudo tee -a /etc/hosts
   echo "127.0.0.1 minio.turdparty.localhost" | sudo tee -a /etc/hosts

Step 4: Start Services
---------------------

.. code-block:: bash

   # Start core infrastructure
   docker-compose -f compose/docker-compose.yml up -d

   # Wait for core services to be ready
   sleep 30

   # Start all worker services
   docker-compose -f compose/docker-compose.workers.yml up -d

   # Start ELK stack
   docker-compose -f compose/docker-compose.elk.yml up -d

   # Wait for ELK stack to initialize
   sleep 60

   # Import Kibana dashboards
   ./services/monitoring/elk/kibana/import-dashboards.sh

   # Validate complete system
   ./scripts/test-phase3-elk-integration.sh

🔍 Service Verification
=======================

Health Check Sequence
--------------------

.. mermaid::

   sequenceDiagram
       participant User
       participant Traefik
       participant API
       participant Database
       participant Storage
       participant Monitoring
       
       User->>Traefik: Check proxy status
       Traefik->>User: ✅ Proxy ready
       
       User->>API: GET /health
       API->>Database: Check connection
       Database->>API: ✅ Connected
       API->>Storage: Check MinIO
       Storage->>API: ✅ Available
       API->>User: ✅ Healthy
       
       User->>Monitoring: Check Kibana
       Monitoring->>User: ✅ Dashboard ready

Verification Commands
--------------------

.. code-block:: bash

   # Check all services are running
   docker-compose -f compose/docker-compose.yml ps
   docker-compose -f compose/docker-compose.workers.yml ps
   docker-compose -f compose/docker-compose.elk.yml ps

   # Verify API health
   curl http://localhost:8000/health

   # Check status dashboard (NEW)
   curl http://status.turdparty.localhost
   curl http://localhost:8000/api/v1/health/celery

   # Check worker health
   curl http://flower.turdparty.localhost/api/workers

   # Verify ELK stack
   curl http://elasticsearch.turdparty.localhost/_cluster/health
   curl http://kibana.turdparty.localhost/api/status

   # Check TurdParty indices
   curl "http://elasticsearch.turdparty.localhost/_cat/indices/turdparty-*?v"

   # Test complete system
   ./scripts/test-phase3-elk-integration.sh

Expected Service Status
----------------------

.. list-table:: Service Health Check
   :header-rows: 1
   :widths: 25 25 25 25

   * - Service
     - URL
     - Expected Response
     - Status
   * - **API Health**
     - http://localhost:8000/health
     - ``{"status": "healthy"}``
     - ✅ Ready
   * - **Frontend**
     - http://frontend.turdparty.localhost
     - React application
     - ✅ Ready
   * - **Status Dashboard**
     - http://status.turdparty.localhost
     - **NEW** System monitoring with Celery metrics
     - ✅ Ready
   * - **Kibana Dashboards**
     - http://kibana.turdparty.localhost
     - 4 comprehensive dashboards
     - ✅ Ready
   * - **Flower Monitor**
     - http://flower.turdparty.localhost
     - Task queue monitoring
     - ✅ Ready
   * - **Elasticsearch**
     - http://elasticsearch.turdparty.localhost
     - Search and analytics
     - ✅ Ready
   * - **MinIO Console**
     - http://minio.turdparty.localhost:9001
     - Storage console
     - ✅ Ready

🌐 Web Interface Access
=======================

Service Dashboard
----------------

.. mermaid::

   graph TB
       subgraph "User Interfaces"
           FRONTEND[React Frontend<br/>frontend.turdparty.localhost]
           DOCS[Documentation<br/>docs.turdparty.localhost]
           API_DOCS[API Documentation<br/>localhost:8000/docs]
       end
       
       subgraph "Administrative Interfaces"
           KIBANA[Kibana Dashboard<br/>kibana.turdparty.localhost]
           MINIO[MinIO Console<br/>minio.turdparty.localhost:9001]
           STATUS[Status Page<br/>status.turdparty.localhost]
       end
       
       subgraph "Development Tools"
           TRAEFIK[Traefik Dashboard<br/>localhost:8080]
           REDOC[ReDoc API<br/>localhost:8000/redoc]
           SWAGGER[Swagger UI<br/>localhost:8000/docs]
       end

Access URLs
----------

**Primary Interfaces:**

- **React UI**: http://frontend.turdparty.localhost - File upload and analysis interface
- **Status Dashboard**: http://status.turdparty.localhost - **NEW** Real-time system monitoring
- **API Documentation**: http://localhost:8000/docs - Interactive API documentation

**Analytics and Monitoring:**

- **Status Dashboard**: http://localhost:8090 - **NEW** Direct access to unified monitoring
- **Kibana Dashboards**: http://kibana.turdparty.localhost - 4 comprehensive dashboards
- **Flower Monitor**: http://flower.turdparty.localhost - Task queue and worker monitoring
- **Elasticsearch**: http://elasticsearch.turdparty.localhost - Search and analytics engine

**Administrative Tools:**

- **MinIO Console**: http://minio.turdparty.localhost:9001 - File storage management
- **ReDoc API**: http://localhost:8000/redoc - Alternative API documentation
- **Health Endpoint**: http://localhost:8000/health - System health status

🔧 Post-Installation Configuration
==================================

Initial Setup Tasks
------------------

.. code-block:: bash

   # Restart all services with comprehensive logging
   ./scripts/restart-with-logging.sh

   # Create MinIO buckets
   ./scripts/setup-minio-buckets.sh

   # Initialize database schema
   ./scripts/init-database.sh

   # Configure Elasticsearch indices
   ./scripts/setup-elasticsearch.sh

   # Import VM templates
   ./scripts/import-vm-templates.sh

Optional VM Setup
----------------

.. code-block:: bash

   # Install Vagrant VMs (optional)
   cd vagrant/
   vagrant up ubuntu-analysis
   vagrant up windows-analysis
   
   # Verify VM connectivity
   ./scripts/test-vm-connectivity.sh

🚨 Troubleshooting
==================

Common Issues
------------

.. admonition:: Port Conflicts
   :class: warning

   **Issue**: Port 80/443 already in use  
   **Solution**: Stop conflicting services or change Traefik ports

.. admonition:: Memory Issues
   :class: warning

   **Issue**: Services failing to start due to memory  
   **Solution**: Increase Docker memory limits or reduce concurrent services

.. admonition:: Network Issues
   :class: warning

   **Issue**: Services not accessible via domain names  
   **Solution**: Verify /etc/hosts configuration and Docker networks

Debug Commands
-------------

.. code-block:: bash

   # Check service logs
   docker-compose logs api
   docker-compose logs traefik
   
   # Inspect Docker networks
   docker network ls
   docker network inspect turdpartycollab_net
   
   # Check resource usage
   docker stats
   
   # Restart specific service
   docker-compose restart api

🎯 Next Steps
=============

Now that TurdParty is installed:

1. **First Analysis**: :doc:`first-analysis` - Run your first malware analysis
2. **System Components**: :doc:`../components/api-layer` - Understand the architecture  
3. **Development Guide**: :doc:`../development/testing-guide` - Set up development environment

📚 Additional Resources
======================

- **Docker Compose Reference**: :doc:`../../deployment/docker_compose`
- **Production Deployment**: :doc:`../../deployment/production`
- **Troubleshooting Guide**: :doc:`../../reference/troubleshooting`
- **Configuration Reference**: :doc:`../../reference/configuration`
