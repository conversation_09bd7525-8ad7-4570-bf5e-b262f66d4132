===============================
🚀 Getting Started with TurdParty
===============================

Welcome to TurdParty, a modern malware analysis platform that combines VM management, file injection, and real-time monitoring in a containerised environment.

.. note::
   **Target Audience**: This guide is designed for developers and technical professionals who want to understand and deploy the TurdParty stack.

.. contents:: Table of Contents
   :local:
   :depth: 3

📋 Overview
===========

TurdParty is a comprehensive malware analysis platform that provides:

- **Automated VM provisioning** for safe malware execution
- **File injection workflows** with UUID tracking
- **Real-time monitoring** via WebSocket streams
- **Comprehensive ELK logging** with centralized log collection from all Docker services
- **RESTful API** for programmatic access

🏗️ System Architecture
======================

High-Level Architecture
-----------------------

.. mermaid::

   graph TB
       subgraph "Frontend Layer"
           UI[React Frontend<br/>frontend.turdparty.localhost]
           DOCS[Documentation<br/>docs.turdparty.localhost]
       end
       
       subgraph "API Gateway"
           TRAEFIK[Traefik Reverse Proxy<br/>Port 80/443]
       end
       
       subgraph "Core Services"
           API[FastAPI Backend<br/>Port 8000]
           WORKER[Celery Workers<br/>Background Tasks]
       end
       
       subgraph "Storage Layer"
           MINIO[MinIO Object Storage<br/>File Repository]
           POSTGRES[PostgreSQL<br/>Metadata & State]
           REDIS[Redis<br/>Task Queue & Cache]
       end
       
       subgraph "VM Management"
           VAGRANT[Vagrant VMs<br/>Linux & Windows]
           DOCKER[Docker VMs<br/>Lightweight Analysis]
           GRPC[gRPC Service<br/>VM Communication]
       end
       
       subgraph "Monitoring Stack"
           ELASTIC[Elasticsearch<br/>Log Storage]
           KIBANA[Kibana Dashboard<br/>kibana.turdparty.localhost]
           LOGSTASH[Logstash<br/>Log Processing]
           FILEBEAT[Filebeat<br/>Docker Log Collection]
       end
       
       subgraph "Status Monitoring"
           CACHET[Cachet Status Page<br/>status.turdparty.localhost]
       end
       
       UI --> TRAEFIK
       DOCS --> TRAEFIK
       TRAEFIK --> API
       TRAEFIK --> KIBANA
       TRAEFIK --> CACHET
       
       API --> MINIO
       API --> POSTGRES
       API --> REDIS
       API --> GRPC
       
       WORKER --> REDIS
       WORKER --> MINIO
       WORKER --> GRPC
       
       GRPC --> VAGRANT
       GRPC --> DOCKER
       
       API --> ELASTIC
       WORKER --> ELASTIC
       VAGRANT --> LOGSTASH
       DOCKER --> LOGSTASH
       LOGSTASH --> ELASTIC
       ELASTIC --> KIBANA

Core Workflow
-------------

.. mermaid::

   sequenceDiagram
       participant User
       participant API
       participant MinIO
       participant Worker
       participant VM
       participant ELK
       
       User->>API: 1. Upload malware file
       API->>MinIO: 2. Store file with UUID
       API->>Worker: 3. Queue analysis task
       
       Worker->>MinIO: 4. Download file
       Worker->>VM: 5. Provision & inject file
       
       VM->>ELK: 6. Stream runtime data
       VM->>Worker: 7. Report completion
       
       Worker->>VM: 8. Destroy VM (30min timeout)
       Worker->>API: 9. Update task status
       
       User->>API: 10. Query results
       API->>ELK: 11. Fetch analysis data
       ELK->>User: 12. Return insights

🔧 Component Deep Dive
======================

Let's explore each component in detail:

.. toctree::
   :maxdepth: 2
   :caption: System Components

   components/api-layer
   components/storage-systems
   components/vm-management
   components/monitoring-stack

🚀 Quick Start Guide
===================

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   quickstart/prerequisites
   quickstart/installation

.. toctree::
   :maxdepth: 2
   :caption: Operations

   operations/logging-operations

📡 API Reference
===============

Complete API documentation for developers:

.. toctree::
   :maxdepth: 2
   :caption: API Documentation

   api-reference

🔗 External Links
================

- **API Documentation**: `localhost:8000/docs <http://localhost:8000/docs>`_
- **ReDoc API**: `localhost:8000/redoc <http://localhost:8000/redoc>`_
- **Kibana Dashboard**: `kibana.turdparty.localhost <http://kibana.turdparty.localhost>`_
- **Status Page**: `status.turdparty.localhost <http://status.turdparty.localhost>`_
- **Main Documentation**: `docs.turdparty.localhost <http://docs.turdparty.localhost>`_

.. note::
   All services use the ``turdparty.localhost`` domain with Traefik routing. Ensure your local DNS or hosts file is configured appropriately.
