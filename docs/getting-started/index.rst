===============================
🚀 Getting Started with TurdParty
===============================

Welcome to TurdParty, a production-ready malware analysis platform that provides automated file processing, real-time VM monitoring, and advanced threat detection capabilities.

.. note::
   **Target Audience**: This guide is designed for security researchers, malware analysts, and technical professionals who want to deploy and operate the TurdParty platform.

.. contents:: Table of Contents
   :local:
   :depth: 3

📋 Overview
===========

TurdParty is a comprehensive malware analysis platform that provides:

- **Automated Malware Analysis** - Complete end-to-end file processing pipeline
- **Intelligent VM Pool Management** - Maintains 2-10 ready VMs for immediate allocation
- **Real-time VM Monitoring** - Comprehensive agent-based monitoring with threat detection
- **Advanced Analytics** - ELK stack integration with 4 specialized Kibana dashboards
- **Workflow Orchestration** - 6 specialized worker types for optimized processing
- **Production-Ready Security** - Complete isolation, monitoring, and operational excellence

🏗️ System Architecture
======================

High-Level Architecture
-----------------------

.. mermaid::

   graph TB
       subgraph "User Interface"
           UI[React Frontend<br/>frontend.turdparty.localhost]
           KB[Kibana Dashboards<br/>kibana.turdparty.localhost]
           FLOWER[Task Monitor<br/>flower.turdparty.localhost]
       end

       subgraph "API Layer"
           API[FastAPI Server<br/>Port 8000]
       end

       subgraph "Worker Layer"
           WF[Workflow Orchestrator]
           FILE[File Operations]
           VM[VM Management]
           INJ[File Injection]
           POOL[VM Pool Manager]
           ELK[ELK Integration]
       end

       subgraph "VM Runtime"
           VM1[VM + Agent]
           VM2[VM + Agent]
           VM3[VM + Agent]
       end

       subgraph "ELK Stack"
           ES[Elasticsearch]
           LS[Logstash]
           KB2[Kibana]
       end

       subgraph "Infrastructure"
           REDIS[Redis Queue]
           DB[PostgreSQL]
           MINIO[MinIO Storage]
       end

       UI --> API
       API --> WF
       WF --> FILE
       WF --> VM
       WF --> INJ
       WF --> POOL
       WF --> ELK

       INJ --> VM1
       INJ --> VM2
       INJ --> VM3

       VM1 --> LS
       VM2 --> LS
       VM3 --> LS
       ELK --> LS
       LS --> ES
       ES --> KB
       KB --> KB2

       FILE --> MINIO
       WF --> REDIS
       WF --> DB
       FLOWER --> REDIS

Complete Analysis Workflow
--------------------------

.. mermaid::

   sequenceDiagram
       participant User
       participant UI as React UI
       participant API as FastAPI
       participant WF as Workflow Orchestrator
       participant POOL as VM Pool
       participant VM as Virtual Machine
       participant AGENT as VM Agent
       participant ELK as ELK Stack
       participant KB as Kibana

       User->>UI: Upload File
       UI->>API: POST /api/v1/files/upload
       API->>WF: Start Workflow
       WF->>POOL: Get Ready VM
       POOL->>VM: Allocate VM
       WF->>VM: Download & Inject File
       WF->>AGENT: Deploy Monitoring Agent
       AGENT->>ELK: Stream Real-time Metrics
       VM->>ELK: Runtime Analysis Data
       ELK->>KB: Real-time Visualization
       WF->>User: Analysis Complete (30min)
       WF->>POOL: Terminate & Replace VM

🔧 Component Deep Dive
======================

Let's explore each component in detail:

.. toctree::
   :maxdepth: 2
   :caption: System Components

   components/api-layer
   components/storage-systems
   components/vm-management
   components/monitoring-stack

🚀 Quick Start Guide
===================

.. toctree::
   :maxdepth: 2
   :caption: Getting Started

   quickstart/prerequisites
   quickstart/installation

.. toctree::
   :maxdepth: 2
   :caption: Operations

   operations/logging-operations

📡 API Reference
===============

Complete API documentation for developers:

.. toctree::
   :maxdepth: 2
   :caption: API Documentation

   api-reference

🔗 Platform Access Points
========================

Once deployed, access the platform through these endpoints:

**Primary Interfaces**:
- **React UI**: `frontend.turdparty.localhost <http://frontend.turdparty.localhost>`_ - Main user interface
- **Kibana Dashboards**: `kibana.turdparty.localhost <http://kibana.turdparty.localhost>`_ - Analytics and monitoring
- **API Documentation**: `localhost:8000/docs <http://localhost:8000/docs>`_ - Interactive API docs

**Monitoring and Management**:
- **Flower Dashboard**: `flower.turdparty.localhost <http://flower.turdparty.localhost>`_ - Task queue monitoring
- **ReDoc API**: `localhost:8000/redoc <http://localhost:8000/redoc>`_ - Alternative API documentation

**Key Features Available**:
- **File Upload and Analysis** - Drag-and-drop malware analysis
- **Real-time Monitoring** - Live VM behavior tracking
- **Threat Detection** - Automated IOC extraction and scoring
- **Performance Analytics** - System health and optimization insights

.. note::
   All services use the ``turdparty.localhost`` domain with Traefik routing. The platform provides a complete malware analysis pipeline from file upload to threat intelligence.
