#!/bin/bash

# Test Coverage Management Script for TurdParty
# Manages test coverage reports, keeps latest, and provides coverage analysis

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COVERAGE_DIR="test-results"
COVERAGE_ARCHIVE_DIR="coverage-archive"
COVERAGE_THRESHOLD=80
MAX_ARCHIVE_DAYS=30
LATEST_SYMLINK_DIR="coverage-latest"

# Functions
print_header() {
    echo -e "${BLUE}================================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

create_directories() {
    print_header "Setting Up Coverage Directories"
    
    mkdir -p "$COVERAGE_DIR"
    mkdir -p "$COVERAGE_ARCHIVE_DIR"
    mkdir -p "$LATEST_SYMLINK_DIR"
    
    # Create .gitkeep files for empty directories
    touch "$COVERAGE_ARCHIVE_DIR/.gitkeep"
    
    print_success "Coverage directories created"
}

archive_previous_coverage() {
    print_header "Archiving Previous Coverage Reports"
    
    if [ -d "$COVERAGE_DIR" ] && [ "$(ls -A $COVERAGE_DIR 2>/dev/null)" ]; then
        timestamp=$(date +"%Y%m%d_%H%M%S")
        archive_path="$COVERAGE_ARCHIVE_DIR/coverage_$timestamp"
        
        # Create archive directory
        mkdir -p "$archive_path"
        
        # Move coverage files to archive
        if [ -f "$COVERAGE_DIR/coverage.xml" ]; then
            cp "$COVERAGE_DIR/coverage.xml" "$archive_path/"
        fi
        
        if [ -d "$COVERAGE_DIR/htmlcov" ]; then
            cp -r "$COVERAGE_DIR/htmlcov" "$archive_path/"
        fi
        
        if [ -f "$COVERAGE_DIR/coverage.json" ]; then
            cp "$COVERAGE_DIR/coverage.json" "$archive_path/"
        fi
        
        # Create metadata file
        cat > "$archive_path/metadata.json" << EOF
{
    "timestamp": "$timestamp",
    "date": "$(date -Iseconds)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')",
    "coverage_threshold": $COVERAGE_THRESHOLD
}
EOF
        
        print_success "Previous coverage archived to $archive_path"
    else
        print_warning "No previous coverage to archive"
    fi
}

update_latest_symlinks() {
    print_header "Updating Latest Coverage Symlinks"
    
    # Remove existing symlinks
    rm -rf "$LATEST_SYMLINK_DIR"/*
    
    # Create new symlinks to latest coverage
    if [ -f "$COVERAGE_DIR/coverage.xml" ]; then
        ln -sf "../$COVERAGE_DIR/coverage.xml" "$LATEST_SYMLINK_DIR/coverage-latest.xml"
        print_success "Created symlink: coverage-latest.xml"
    fi
    
    if [ -f "$COVERAGE_DIR/coverage.html" ]; then
        ln -sf "../$COVERAGE_DIR/coverage.html" "$LATEST_SYMLINK_DIR/coverage-latest.html"
        print_success "Created symlink: coverage-latest.html"
    fi
    
    if [ -d "$COVERAGE_DIR/htmlcov" ]; then
        ln -sf "../$COVERAGE_DIR/htmlcov" "$LATEST_SYMLINK_DIR/htmlcov-latest"
        print_success "Created symlink: htmlcov-latest"
    fi
    
    if [ -f "$COVERAGE_DIR/coverage.json" ]; then
        ln -sf "../$COVERAGE_DIR/coverage.json" "$LATEST_SYMLINK_DIR/coverage-latest.json"
        print_success "Created symlink: coverage-latest.json"
    fi
}

analyze_coverage() {
    print_header "Analyzing Coverage Results"
    
    if [ ! -f "$COVERAGE_DIR/coverage.xml" ]; then
        print_error "No coverage.xml found. Run tests first."
        return 1
    fi
    
    # Extract coverage percentage from XML
    coverage_percentage=$(python3 -c "
import xml.etree.ElementTree as ET
import sys
try:
    tree = ET.parse('$COVERAGE_DIR/coverage.xml')
    root = tree.getroot()
    coverage = float(root.attrib['line-rate']) * 100
    print(f'{coverage:.1f}')
except Exception as e:
    print('0.0')
    sys.exit(1)
" 2>/dev/null || echo "0.0")
    
    echo "Current Coverage: ${coverage_percentage}%"
    echo "Coverage Threshold: ${COVERAGE_THRESHOLD}%"
    
    # Check if coverage meets threshold
    if (( $(echo "$coverage_percentage >= $COVERAGE_THRESHOLD" | bc -l) )); then
        print_success "Coverage meets threshold (${coverage_percentage}% >= ${COVERAGE_THRESHOLD}%)"
        coverage_status="PASS"
    else
        print_warning "Coverage below threshold (${coverage_percentage}% < ${COVERAGE_THRESHOLD}%)"
        coverage_status="FAIL"
    fi
    
    # Generate coverage summary
    cat > "$COVERAGE_DIR/coverage-summary.json" << EOF
{
    "coverage_percentage": $coverage_percentage,
    "coverage_threshold": $COVERAGE_THRESHOLD,
    "status": "$coverage_status",
    "timestamp": "$(date -Iseconds)",
    "git_commit": "$(git rev-parse HEAD 2>/dev/null || echo 'unknown')",
    "git_branch": "$(git branch --show-current 2>/dev/null || echo 'unknown')"
}
EOF
    
    # Show detailed coverage if available
    if command -v coverage &> /dev/null && [ -f ".coverage" ]; then
        echo ""
        echo "Detailed Coverage Report:"
        coverage report --show-missing | head -20
        
        # Show files with low coverage
        echo ""
        echo "Files with coverage below ${COVERAGE_THRESHOLD}%:"
        coverage report --show-missing | awk -v threshold="$COVERAGE_THRESHOLD" '
        NR > 2 && $NF != "100%" {
            gsub(/%/, "", $NF)
            if ($NF < threshold && $NF != "TOTAL") {
                print $1 ": " $NF "%"
            }
        }'
    fi
}

compare_coverage() {
    print_header "Comparing Coverage with Previous Run"
    
    current_coverage=$(python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('$COVERAGE_DIR/coverage.xml')
    root = tree.getroot()
    coverage = float(root.attrib['line-rate']) * 100
    print(f'{coverage:.1f}')
except:
    print('0.0')
" 2>/dev/null || echo "0.0")
    
    # Find most recent archived coverage
    latest_archive=$(ls -t "$COVERAGE_ARCHIVE_DIR" 2>/dev/null | head -1)
    
    if [ -n "$latest_archive" ] && [ -f "$COVERAGE_ARCHIVE_DIR/$latest_archive/coverage.xml" ]; then
        previous_coverage=$(python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('$COVERAGE_ARCHIVE_DIR/$latest_archive/coverage.xml')
    root = tree.getroot()
    coverage = float(root.attrib['line-rate']) * 100
    print(f'{coverage:.1f}')
except:
    print('0.0')
" 2>/dev/null || echo "0.0")
        
        # Calculate difference
        coverage_diff=$(python3 -c "print(f'{float('$current_coverage') - float('$previous_coverage'):.1f}')")
        
        echo "Current Coverage: ${current_coverage}%"
        echo "Previous Coverage: ${previous_coverage}%"
        echo "Difference: ${coverage_diff}%"
        
        if (( $(echo "$coverage_diff > 0" | bc -l) )); then
            print_success "Coverage improved by ${coverage_diff}%"
        elif (( $(echo "$coverage_diff < 0" | bc -l) )); then
            print_warning "Coverage decreased by ${coverage_diff#-}%"
        else
            echo "Coverage unchanged"
        fi
    else
        print_warning "No previous coverage data for comparison"
    fi
}

cleanup_old_archives() {
    print_header "Cleaning Up Old Coverage Archives"
    
    if [ ! -d "$COVERAGE_ARCHIVE_DIR" ]; then
        print_warning "No archive directory found"
        return 0
    fi
    
    # Find archives older than MAX_ARCHIVE_DAYS
    old_archives=$(find "$COVERAGE_ARCHIVE_DIR" -type d -name "coverage_*" -mtime +$MAX_ARCHIVE_DAYS 2>/dev/null || true)
    
    if [ -n "$old_archives" ]; then
        echo "Removing archives older than $MAX_ARCHIVE_DAYS days:"
        echo "$old_archives" | while read -r archive; do
            if [ -d "$archive" ]; then
                echo "  Removing: $(basename "$archive")"
                rm -rf "$archive"
            fi
        done
        print_success "Old archives cleaned up"
    else
        print_success "No old archives to clean up"
    fi
}

generate_coverage_badge() {
    print_header "Generating Coverage Badge"
    
    if [ ! -f "$COVERAGE_DIR/coverage.xml" ]; then
        print_error "No coverage data available"
        return 1
    fi
    
    coverage_percentage=$(python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('$COVERAGE_DIR/coverage.xml')
    root = tree.getroot()
    coverage = float(root.attrib['line-rate']) * 100
    print(f'{coverage:.0f}')
except:
    print('0')
" 2>/dev/null || echo "0")
    
    # Determine badge color based on coverage
    if [ "$coverage_percentage" -ge 90 ]; then
        color="brightgreen"
    elif [ "$coverage_percentage" -ge 80 ]; then
        color="green"
    elif [ "$coverage_percentage" -ge 70 ]; then
        color="yellow"
    elif [ "$coverage_percentage" -ge 60 ]; then
        color="orange"
    else
        color="red"
    fi
    
    # Generate badge URL
    badge_url="https://img.shields.io/badge/coverage-${coverage_percentage}%25-${color}"
    
    # Create badge markdown
    cat > "$COVERAGE_DIR/coverage-badge.md" << EOF
[![Coverage](${badge_url})](./htmlcov/index.html)

**Coverage: ${coverage_percentage}%**

Generated on: $(date)
EOF
    
    echo "Coverage badge URL: $badge_url"
    print_success "Coverage badge generated"
}

show_coverage_trends() {
    print_header "Coverage Trends"
    
    if [ ! -d "$COVERAGE_ARCHIVE_DIR" ]; then
        print_warning "No archive data available for trends"
        return 0
    fi
    
    echo "Recent coverage history:"
    echo "Date                Coverage"
    echo "=================== ========"
    
    # Show last 10 coverage results
    for archive in $(ls -t "$COVERAGE_ARCHIVE_DIR" | head -10); do
        if [ -f "$COVERAGE_ARCHIVE_DIR/$archive/coverage.xml" ]; then
            coverage=$(python3 -c "
import xml.etree.ElementTree as ET
try:
    tree = ET.parse('$COVERAGE_ARCHIVE_DIR/$archive/coverage.xml')
    root = tree.getroot()
    coverage = float(root.attrib['line-rate']) * 100
    print(f'{coverage:.1f}%')
except:
    print('N/A')
" 2>/dev/null || echo "N/A")
            
            # Extract date from archive name
            date_part=$(echo "$archive" | sed 's/coverage_//' | sed 's/_/ /')
            formatted_date=$(date -d "${date_part:0:8} ${date_part:9:2}:${date_part:11:2}:${date_part:13:2}" "+%Y-%m-%d %H:%M" 2>/dev/null || echo "$date_part")
            
            printf "%-19s %s\n" "$formatted_date" "$coverage"
        fi
    done
}

main() {
    case "${1:-help}" in
        "setup")
            create_directories
            ;;
        "archive")
            archive_previous_coverage
            ;;
        "analyze")
            analyze_coverage
            ;;
        "compare")
            compare_coverage
            ;;
        "update-links")
            update_latest_symlinks
            ;;
        "cleanup")
            cleanup_old_archives
            ;;
        "badge")
            generate_coverage_badge
            ;;
        "trends")
            show_coverage_trends
            ;;
        "full")
            create_directories
            archive_previous_coverage
            analyze_coverage
            compare_coverage
            update_latest_symlinks
            generate_coverage_badge
            cleanup_old_archives
            ;;
        "help"|*)
            echo "TurdParty Coverage Management Script"
            echo ""
            echo "Usage: $0 [command]"
            echo ""
            echo "Commands:"
            echo "  setup        Create coverage directories"
            echo "  archive      Archive previous coverage reports"
            echo "  analyze      Analyze current coverage results"
            echo "  compare      Compare with previous coverage"
            echo "  update-links Update symlinks to latest coverage"
            echo "  cleanup      Remove old archived coverage"
            echo "  badge        Generate coverage badge"
            echo "  trends       Show coverage trends"
            echo "  full         Run complete coverage management workflow"
            echo "  help         Show this help message"
            echo ""
            echo "Configuration:"
            echo "  Coverage threshold: ${COVERAGE_THRESHOLD}%"
            echo "  Archive retention: ${MAX_ARCHIVE_DAYS} days"
            echo "  Coverage directory: ${COVERAGE_DIR}"
            echo "  Archive directory: ${COVERAGE_ARCHIVE_DIR}"
            ;;
    esac
}

# Check dependencies
if ! command -v bc &> /dev/null; then
    print_error "bc is required but not installed"
    exit 1
fi

# Run main function
main "$@"
