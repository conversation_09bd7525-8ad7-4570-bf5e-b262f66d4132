#!/bin/bash
# Restart TurdParty services with comprehensive ELK logging

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Print functions
print_header() {
    echo -e "\n${PURPLE}╔══════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${PURPLE}║${NC} ${CYAN}$1${NC} ${PURPLE}║${NC}"
    echo -e "${PURPLE}╚══════════════════════════════════════════════════════════════╝${NC}\n"
}

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -f "$PROJECT_ROOT/docker-compose.yml" ]; then
        print_error "Not in TurdParty project root directory"
        exit 1
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose &> /dev/null && ! command -v nix-shell &> /dev/null; then
        print_error "Neither docker-compose nor nix-shell found"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Function to create required directories
create_directories() {
    print_status "Creating required directories..."
    
    mkdir -p "$PROJECT_ROOT/config/filebeat"
    mkdir -p "$PROJECT_ROOT/logs"
    
    print_success "Directories created"
}

# Function to stop existing services
stop_services() {
    print_status "Stopping existing services..."
    
    cd "$PROJECT_ROOT"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose down
    else
        nix-shell -p docker-compose --run "docker-compose down"
    fi
    
    print_success "Services stopped"
}

# Function to start core services first
start_core_services() {
    print_status "Starting core services (Elasticsearch, Logstash)..."
    
    cd "$PROJECT_ROOT"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d elasticsearch logstash
    else
        nix-shell -p docker-compose --run "docker-compose up -d elasticsearch logstash"
    fi
    
    print_status "Waiting for core services to be ready..."
    sleep 30
    
    print_success "Core services started"
}

# Function to start log collection
start_log_collection() {
    print_status "Starting log collection services (Filebeat)..."
    
    cd "$PROJECT_ROOT"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d filebeat
    else
        nix-shell -p docker-compose --run "docker-compose up -d filebeat"
    fi
    
    print_status "Waiting for log collection to initialize..."
    sleep 15
    
    print_success "Log collection started"
}

# Function to start application services
start_application_services() {
    print_status "Starting application services..."
    
    cd "$PROJECT_ROOT"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d api kibana vm-monitor
    else
        nix-shell -p docker-compose --run "docker-compose up -d api kibana vm-monitor"
    fi
    
    print_status "Waiting for application services..."
    sleep 20
    
    print_success "Application services started"
}

# Function to start web services
start_web_services() {
    print_status "Starting web services..."
    
    cd "$PROJECT_ROOT"
    
    if command -v docker-compose &> /dev/null; then
        docker-compose up -d frontend docs status
    else
        nix-shell -p docker-compose --run "docker-compose up -d frontend docs status"
    fi
    
    print_success "Web services started"
}

# Function to verify services
verify_services() {
    print_status "Verifying service health..."
    
    cd "$PROJECT_ROOT"
    
    # Check service status
    if command -v docker-compose &> /dev/null; then
        docker-compose ps
    else
        nix-shell -p docker-compose --run "docker-compose ps"
    fi
    
    print_status "Checking Elasticsearch health..."
    sleep 5
    if curl -s http://localhost:9200/_cluster/health | grep -q "green\|yellow"; then
        print_success "Elasticsearch is healthy"
    else
        print_warning "Elasticsearch may not be fully ready yet"
    fi
    
    print_status "Checking API health..."
    if curl -s http://localhost:8000/health | grep -q "healthy"; then
        print_success "API is healthy"
    else
        print_warning "API may not be fully ready yet"
    fi
}

# Function to show log access information
show_log_access() {
    print_header "📊 Log Access Information"
    
    echo -e "${GREEN}🔍 Elasticsearch Indices:${NC}"
    echo -e "  • Docker Logs: ${CYAN}turdparty-docker-*${NC}"
    echo -e "  • Application Logs: ${CYAN}turdparty-*${NC}"
    echo -e "  • File Injection Logs: ${CYAN}turdparty-file_injection-*${NC}"
    echo -e "  • Installation Logs: ${CYAN}turdparty-installation-*${NC}"
    
    echo -e "\n${GREEN}📈 Kibana Dashboard:${NC}"
    echo -e "  • URL: ${CYAN}http://kibana.turdparty.localhost${NC}"
    echo -e "  • Index Patterns: ${CYAN}turdparty-*${NC}"
    
    echo -e "\n${GREEN}🔧 Log Sources:${NC}"
    echo -e "  • Docker Container Logs: ${CYAN}Filebeat → Logstash → Elasticsearch${NC}"
    echo -e "  • Application Logs: ${CYAN}Direct TCP → Logstash → Elasticsearch${NC}"
    echo -e "  • File Logs: ${CYAN}File Input → Logstash → Elasticsearch${NC}"
    
    echo -e "\n${GREEN}📊 Service Logs:${NC}"
    echo -e "  • API: ${CYAN}turdparty-docker-api-*${NC}"
    echo -e "  • Frontend: ${CYAN}turdparty-docker-frontend-*${NC}"
    echo -e "  • Elasticsearch: ${CYAN}turdparty-docker-elasticsearch-*${NC}"
    echo -e "  • Logstash: ${CYAN}turdparty-docker-logstash-*${NC}"
    echo -e "  • Kibana: ${CYAN}turdparty-docker-kibana-*${NC}"
    echo -e "  • VM Monitor: ${CYAN}turdparty-docker-vm-monitor-*${NC}"
    echo -e "  • Documentation: ${CYAN}turdparty-docker-docs-*${NC}"
    echo -e "  • Status: ${CYAN}turdparty-docker-status-*${NC}"
}

# Function to show usage
show_usage() {
    echo -e "${CYAN}TurdParty ELK Logging Setup${NC}"
    echo -e "${CYAN}===========================${NC}"
    echo ""
    echo -e "${GREEN}Usage:${NC} $0 [OPTIONS]"
    echo ""
    echo -e "${GREEN}Options:${NC}"
    echo -e "  ${YELLOW}--skip-stop${NC}     Skip stopping existing services"
    echo -e "  ${YELLOW}--help${NC}         Show this help message"
    echo ""
    echo -e "${GREEN}Description:${NC}"
    echo -e "  Restarts all TurdParty services with comprehensive logging to ELK stack."
    echo -e "  All Docker container logs will be collected by Filebeat and sent to Logstash."
    echo -e "  Logs are processed and stored in Elasticsearch with proper indexing."
}

# Main function
main() {
    local skip_stop=false
    
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --skip-stop)
                skip_stop=true
                shift
                ;;
            --help|-h)
                show_usage
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    print_header "🚀 TurdParty ELK Logging Setup"
    
    # Execute setup steps
    check_prerequisites
    create_directories
    
    if [ "$skip_stop" = false ]; then
        stop_services
    fi
    
    start_core_services
    start_log_collection
    start_application_services
    start_web_services
    verify_services
    show_log_access
    
    print_success "TurdParty services restarted with comprehensive ELK logging!"
    print_status "All Docker container logs are now being collected and processed."
    print_status "Access Kibana at: http://kibana.turdparty.localhost"
}

# Run main function with all arguments
main "$@"
