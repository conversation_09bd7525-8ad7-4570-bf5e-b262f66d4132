#!/usr/bin/env python3
"""
TurdParty Windows Developer Binaries End-to-End Test
Uses the designed API endpoints and flows to test 5 top Windows developer applications.
"""

import asyncio
import json
import time
import uuid
from datetime import datetime
from pathlib import Path
import httpx
import hashlib
import sys
import os

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Configuration
API_BASE_URL = "http://localhost:8000"
ELASTICSEARCH_URL = "http://elasticsearch.turdparty.localhost:9200"
MINIO_ENDPOINT = "http://minio.turdparty.localhost:9000"

# Top 5 Windows Developer Binaries
DEVELOPER_BINARIES = [
    {
        "name": "vscode",
        "description": "Visual Studio Code",
        "url": "https://code.visualstudio.com/sha/download?build=stable&os=win32-x64",
        "filename": "VSCodeSetup-x64.exe",
        "type": "msi",
        "install_command": "VSCodeSetup-x64.exe /VERYSILENT /NORESTART /MERGETASKS=!runcode"
    },
    {
        "name": "python",
        "description": "Python Programming Language",
        "url": "https://www.python.org/ftp/python/3.12.1/python-3.12.1-amd64.exe",
        "filename": "python-3.12.1-amd64.exe",
        "type": "msi",
        "install_command": "python-3.12.1-amd64.exe /quiet InstallAllUsers=1 PrependPath=1"
    },
    {
        "name": "git",
        "description": "Git Version Control System",
        "url": "https://github.com/git-for-windows/git/releases/download/v2.42.0.windows.2/Git-********-64-bit.exe",
        "filename": "Git-********-64-bit.exe",
        "type": "msi",
        "install_command": "Git-********-64-bit.exe /VERYSILENT /NORESTART"
    },
    {
        "name": "nodejs",
        "description": "Node.js JavaScript Runtime",
        "url": "https://nodejs.org/dist/v20.10.0/node-v20.10.0-x64.msi",
        "filename": "node-v20.10.0-x64.msi",
        "type": "msi",
        "install_command": "msiexec /i node-v20.10.0-x64.msi /quiet /norestart"
    },
    {
        "name": "notepadpp",
        "description": "Notepad++ Text Editor",
        "url": "https://github.com/notepad-plus-plus/notepad-plus-plus/releases/download/v8.5.8/npp.8.5.8.Installer.x64.exe",
        "filename": "npp.8.5.8.Installer.x64.exe",
        "type": "exe",
        "install_command": "npp.8.5.8.Installer.x64.exe /S"
    }
]

class WindowsDevBinaryTester:
    def __init__(self):
        self.session = httpx.AsyncClient(timeout=300.0, follow_redirects=True)
        self.test_results = []
        self.test_start_time = datetime.now()
        
    async def download_binary(self, app: dict) -> tuple[Path, dict]:
        """Download binary and generate metadata."""
        print(f"📥 Downloading {app['name']} ({app['description']})...")
        
        download_dir = Path("/tmp/turdparty-dev-binaries")
        download_dir.mkdir(exist_ok=True)
        
        file_path = download_dir / app['filename']
        
        # Download file
        start_time = time.time()
        async with self.session.stream('GET', app['url']) as response:
            response.raise_for_status()
            
            with file_path.open('wb') as f:
                async for chunk in response.aiter_bytes():
                    f.write(chunk)
        
        download_time = time.time() - start_time
        
        # Generate metadata
        file_uuid = str(uuid.uuid4())
        file_size = file_path.stat().st_size
        
        # Calculate Blake3 hash
        import blake3
        hasher = blake3.blake3()
        with file_path.open('rb') as f:
            for chunk in iter(lambda: f.read(8192), b""):
                hasher.update(chunk)
        blake3_hash = hasher.hexdigest()
        
        metadata = {
            'file_uuid': file_uuid,
            'filename': app['filename'],
            'file_size': file_size,
            'blake3_hash': blake3_hash,
            'download_time_seconds': download_time,
            'download_url': app['url'],
            'application_name': app['name'],
            'application_description': app['description'],
            'file_type': app['type'],
            'platform': 'windows',
            'timestamp': datetime.now().isoformat()
        }
        
        print(f"✅ Downloaded {app['name']}: {file_size:,} bytes in {download_time:.2f}s")
        print(f"   UUID: {file_uuid}")
        print(f"   Blake3: {blake3_hash[:16]}...")
        
        return file_path, metadata

    async def upload_to_minio_via_api(self, file_path: Path, metadata: dict) -> dict:
        """Upload file to MinIO using TurdParty API."""
        print(f"📤 Uploading {metadata['filename']} to MinIO via API...")
        
        start_time = time.time()
        
        # Use the file upload API endpoint
        with file_path.open('rb') as f:
            files = {'file': (metadata['filename'], f, 'application/octet-stream')}
            data = {
                'description': f"Developer binary: {metadata['application_description']}",
                'metadata': json.dumps(metadata)
            }
            
            response = await self.session.post(
                f"{API_BASE_URL}/api/v1/files/upload",
                files=files,
                data=data
            )
            response.raise_for_status()
        
        upload_time = time.time() - start_time
        upload_result = response.json()
        
        print(f"✅ Uploaded to MinIO in {upload_time:.2f}s")
        print(f"   File ID: {upload_result.get('file_id', 'N/A')}")
        
        return {
            'upload_time_seconds': upload_time,
            'file_id': upload_result.get('file_id'),
            'minio_bucket': upload_result.get('bucket'),
            'minio_object_name': upload_result.get('object_name'),
            'api_response': upload_result
        }

    async def create_windows_vm_via_api(self, app_name: str) -> dict:
        """Create Windows VM using TurdParty API."""
        print(f"🖥️ Creating Windows VM for {app_name}...")
        
        vm_config = {
            'name': f"dev-{app_name}-{int(time.time())}",
            'template': 'gusztavvargadr/windows-10',
            'vm_type': 'vagrant',
            'memory_mb': 4096,
            'cpus': 2,
            'domain': 'TurdParty',
            'description': f'Windows VM for testing {app_name} developer binary'
        }
        
        start_time = time.time()
        
        response = await self.session.post(
            f"{API_BASE_URL}/api/v1/vms/",
            json=vm_config
        )
        response.raise_for_status()
        
        vm_result = response.json()
        vm_id = vm_result['vm_id']
        
        # Wait for VM to be ready
        print(f"⏳ Waiting for VM {vm_id} to be ready...")
        max_wait = 600  # 10 minutes
        wait_start = time.time()
        
        while time.time() - wait_start < max_wait:
            status_response = await self.session.get(f"{API_BASE_URL}/api/v1/vms/{vm_id}")
            status_response.raise_for_status()
            vm_status = status_response.json()
            
            if vm_status['status'] == 'running':
                break
                
            await asyncio.sleep(10)
        
        boot_time = time.time() - start_time
        
        print(f"✅ VM {vm_id} ready in {boot_time:.2f}s")
        
        return {
            'vm_id': vm_id,
            'vm_name': vm_config['name'],
            'boot_time_seconds': boot_time,
            'vm_config': vm_config,
            'api_response': vm_result
        }

    async def inject_file_via_api(self, vm_id: str, file_id: str, app: dict) -> dict:
        """Inject file into VM using TurdParty API."""
        print(f"💉 Injecting {app['name']} into VM {vm_id}...")
        
        injection_config = {
            'file_id': file_id,
            'target_path': f"C:\\temp\\{app['filename']}",
            'execute_after_injection': False,
            'capture_execution': True
        }
        
        start_time = time.time()
        
        response = await self.session.post(
            f"{API_BASE_URL}/api/v1/vms/{vm_id}/inject",
            json=injection_config
        )
        response.raise_for_status()
        
        injection_result = response.json()
        injection_time = time.time() - start_time
        
        print(f"✅ File injected in {injection_time:.2f}s")
        print(f"   Injection ID: {injection_result.get('injection_id', 'N/A')}")
        
        return {
            'injection_time_seconds': injection_time,
            'injection_id': injection_result.get('injection_id'),
            'target_path': injection_config['target_path'],
            'api_response': injection_result
        }

    async def execute_installation_via_api(self, vm_id: str, app: dict) -> dict:
        """Execute installation in VM using TurdParty API."""
        print(f"⚙️ Installing {app['name']} in VM {vm_id}...")
        
        execution_config = {
            'command': app['install_command'],
            'working_directory': 'C:\\temp',
            'timeout_seconds': 300,
            'capture_output': True,
            'capture_filesystem_changes': True
        }
        
        start_time = time.time()
        
        response = await self.session.post(
            f"{API_BASE_URL}/api/v1/vms/{vm_id}/execute",
            json=execution_config
        )
        response.raise_for_status()
        
        execution_result = response.json()
        execution_time = time.time() - start_time
        
        print(f"✅ Installation completed in {execution_time:.2f}s")
        print(f"   Exit Code: {execution_result.get('exit_code', 'N/A')}")
        
        return {
            'execution_time_seconds': execution_time,
            'execution_id': execution_result.get('execution_id'),
            'exit_code': execution_result.get('exit_code'),
            'success': execution_result.get('exit_code') == 0,
            'api_response': execution_result
        }

    async def cleanup_vm_via_api(self, vm_id: str):
        """Cleanup VM using TurdParty API."""
        print(f"🧹 Cleaning up VM {vm_id}...")
        
        response = await self.session.delete(f"{API_BASE_URL}/api/v1/vms/{vm_id}?force=true")
        response.raise_for_status()
        
        print(f"✅ VM {vm_id} cleaned up")

    async def run_single_binary_test(self, app: dict) -> dict:
        """Run complete test for a single binary."""
        print(f"\n{'='*60}")
        print(f"🧪 Testing {app['name']} - {app['description']}")
        print(f"{'='*60}")
        
        test_start = time.time()
        result = {
            'application': app,
            'success': False,
            'error': None,
            'timestamps': {
                'test_start': datetime.now().isoformat()
            }
        }
        
        try:
            # Step 1: Download binary
            file_path, metadata = await self.download_binary(app)
            result['metadata'] = metadata
            result['file_uuid'] = metadata['file_uuid']
            
            # Step 2: Upload to MinIO via API
            upload_result = await self.upload_to_minio_via_api(file_path, metadata)
            result['upload_result'] = upload_result
            
            # Step 3: Create Windows VM via API
            vm_result = await self.create_windows_vm_via_api(app['name'])
            result['vm_result'] = vm_result
            
            # Step 4: Inject file via API
            injection_result = await self.inject_file_via_api(
                vm_result['vm_id'], 
                upload_result['file_id'], 
                app
            )
            result['injection_result'] = injection_result
            
            # Step 5: Execute installation via API
            execution_result = await self.execute_installation_via_api(
                vm_result['vm_id'], 
                app
            )
            result['execution_result'] = execution_result
            
            # Step 6: Cleanup VM
            await self.cleanup_vm_via_api(vm_result['vm_id'])
            
            result['success'] = execution_result['success']
            result['total_time_seconds'] = time.time() - test_start
            result['timestamps']['test_end'] = datetime.now().isoformat()
            
            print(f"✅ {app['name']} test completed successfully!")
            
        except Exception as e:
            result['error'] = str(e)
            result['total_time_seconds'] = time.time() - test_start
            result['timestamps']['test_end'] = datetime.now().isoformat()
            print(f"❌ {app['name']} test failed: {e}")
            
            # Cleanup on error
            if 'vm_result' in result:
                try:
                    await self.cleanup_vm_via_api(result['vm_result']['vm_id'])
                except:
                    pass
        
        return result

    async def check_services(self):
        """Check if TurdParty services are available."""
        print("🔍 Checking TurdParty services...")

        services = [
            ("API", f"{API_BASE_URL}/health"),
            ("Elasticsearch", f"{ELASTICSEARCH_URL}/_cluster/health"),
        ]

        for service_name, url in services:
            try:
                response = await self.session.get(url)
                if response.status_code == 200:
                    print(f"✅ {service_name}: Available")
                else:
                    print(f"⚠️ {service_name}: Status {response.status_code}")
            except Exception as e:
                print(f"❌ {service_name}: Not available - {e}")

    async def run_all_tests(self):
        """Run tests for all developer binaries."""
        print("🚀 Starting Windows Developer Binaries End-to-End Test")
        print(f"📅 Test started at: {self.test_start_time}")
        print(f"🎯 Testing {len(DEVELOPER_BINARIES)} applications")

        # Check services first
        await self.check_services()

        for i, app in enumerate(DEVELOPER_BINARIES, 1):
            print(f"\n🔄 Progress: {i}/{len(DEVELOPER_BINARIES)}")
            result = await self.run_single_binary_test(app)
            self.test_results.append(result)

        await self.generate_comprehensive_report()

    async def generate_comprehensive_report(self):
        """Generate comprehensive test report with ECS logs."""
        print(f"\n{'='*80}")
        print("📊 COMPREHENSIVE TEST REPORT")
        print(f"{'='*80}")
        
        # Save detailed results
        report_file = f"/tmp/windows-dev-binaries-report-{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump({
                'test_metadata': {
                    'test_start_time': self.test_start_time.isoformat(),
                    'test_end_time': datetime.now().isoformat(),
                    'total_applications': len(DEVELOPER_BINARIES),
                    'api_base_url': API_BASE_URL
                },
                'results': self.test_results
            }, f, indent=2, default=str)
        
        print(f"📄 Detailed report saved: {report_file}")
        
        # Print summary
        successful_tests = [r for r in self.test_results if r['success']]
        failed_tests = [r for r in self.test_results if not r['success']]
        
        print(f"\n📈 SUMMARY:")
        print(f"   Total Tests: {len(self.test_results)}")
        print(f"   Successful: {len(successful_tests)}")
        print(f"   Failed: {len(failed_tests)}")
        print(f"   Success Rate: {len(successful_tests)/len(self.test_results)*100:.1f}%")
        
        print(f"\n🔑 MINIO UUIDS:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            uuid_val = result.get('file_uuid', 'N/A')
            app_name = result['application']['name']
            print(f"   {status} {app_name}: {uuid_val}")
        
        # Query ECS logs from Elasticsearch
        await self.query_ecs_logs()

    async def query_ecs_logs(self):
        """Query ECS logs from Elasticsearch for our test UUIDs."""
        print(f"\n🔍 QUERYING ECS LOGS FROM ELASTICSEARCH")
        print("-" * 50)
        
        try:
            # Get all UUIDs from our tests
            test_uuids = [r.get('file_uuid') for r in self.test_results if r.get('file_uuid')]
            
            if not test_uuids:
                print("⚠️ No UUIDs found to query")
                return
            
            # Query Elasticsearch for logs related to our UUIDs
            query = {
                "query": {
                    "bool": {
                        "should": [
                            {"terms": {"file_uuid.keyword": test_uuids}},
                            {"terms": {"message": test_uuids}}
                        ],
                        "minimum_should_match": 1
                    }
                },
                "sort": [{"@timestamp": {"order": "desc"}}],
                "size": 100
            }
            
            response = await self.session.post(
                f"{ELASTICSEARCH_URL}/turdparty-*/_search",
                json=query
            )
            
            if response.status_code == 200:
                es_result = response.json()
                hits = es_result.get('hits', {}).get('hits', [])
                
                print(f"📊 Found {len(hits)} ECS log entries")
                
                for hit in hits[:10]:  # Show first 10
                    source = hit['_source']
                    timestamp = source.get('@timestamp', 'N/A')
                    message = source.get('message', 'N/A')
                    level = source.get('log', {}).get('level', 'INFO')
                    
                    print(f"   [{timestamp}] {level}: {message[:100]}...")
                
                if len(hits) > 10:
                    print(f"   ... and {len(hits) - 10} more entries")
                    
            else:
                print(f"⚠️ Elasticsearch query failed: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Failed to query ECS logs: {e}")

async def main():
    """Main entry point."""
    tester = WindowsDevBinaryTester()
    try:
        await tester.run_all_tests()
    finally:
        await tester.session.aclose()

if __name__ == "__main__":
    asyncio.run(main())
