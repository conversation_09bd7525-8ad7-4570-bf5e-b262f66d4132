"""
Integration tests for Traefik routing configuration
Tests all TurdParty service endpoints via Traefik
"""

import pytest
import requests
import time
import json
from urllib.parse import urljoin


class TestTraefikRouting:
    """Test Traefik routing for all TurdParty services"""
    
    @pytest.fixture(scope="class")
    def traefik_services(self):
        """All services routed through Traefik"""
        return {
            'frontend': {
                'url': 'http://frontend.turdparty.localhost',
                'expected_content': ['turdparty', 'frontend'],
                'content_type': 'text/html'
            },
            'docs': {
                'url': 'http://docs.turdparty.localhost',
                'expected_content': ['turdparty', 'documentation'],
                'content_type': 'text/html'
            },
            'kibana': {
                'url': 'http://kibana.turdparty.localhost',
                'expected_content': ['kibana', 'elastic'],
                'content_type': 'text/html',
                'optional': True  # Might not be running
            },
            'status': {
                'url': 'http://status.turdparty.localhost',
                'expected_content': ['status', 'turdparty'],
                'content_type': 'text/html',
                'optional': True  # Might not be running
            },
            'elasticsearch': {
                'url': 'http://elasticsearch.turdparty.localhost',
                'expected_content': ['elasticsearch', 'cluster'],
                'content_type': 'application/json',
                'optional': True  # Might not be running
            }
        }
    
    @pytest.fixture(scope="class")
    def direct_services(self):
        """Services accessible directly (not through Traefik)"""
        return {
            'api': {
                'url': 'http://localhost:8000',
                'health_endpoint': '/health',
                'expected_content': ['healthy'],
                'content_type': 'text/plain'
            },
            'api_docs': {
                'url': 'http://localhost:8000/docs',
                'expected_content': ['swagger', 'openapi'],
                'content_type': 'text/html'
            },
            'api_openapi': {
                'url': 'http://localhost:8000/openapi.json',
                'expected_content': ['openapi', 'paths'],
                'content_type': 'application/json'
            }
        }
    
    @pytest.fixture(scope="class")
    def session(self):
        """HTTP session with reasonable timeouts"""
        session = requests.Session()
        session.timeout = 10
        return session
    
    def test_traefik_service_routing(self, traefik_services, session):
        """Test all Traefik-routed services are accessible"""
        results = {}
        
        for service_name, config in traefik_services.items():
            url = config['url']
            optional = config.get('optional', False)
            
            try:
                response = session.get(url, timeout=5)
                results[service_name] = {
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'content_type': response.headers.get('content-type', ''),
                    'response_time': response.elapsed.total_seconds()
                }
                
                if response.status_code == 200:
                    # Check content type
                    expected_type = config['content_type']
                    actual_type = response.headers.get('content-type', '')
                    
                    if expected_type in actual_type:
                        results[service_name]['content_type_match'] = True
                    else:
                        results[service_name]['content_type_match'] = False
                        print(f"Content type mismatch for {service_name}: expected {expected_type}, got {actual_type}")
                    
                    # Check expected content
                    content = response.text.lower()
                    expected_content = config['expected_content']
                    content_found = all(term.lower() in content for term in expected_content)
                    results[service_name]['content_match'] = content_found
                    
                    if not content_found:
                        missing = [term for term in expected_content if term.lower() not in content]
                        print(f"Missing content in {service_name}: {missing}")
                
            except requests.exceptions.RequestException as e:
                results[service_name] = {
                    'accessible': False,
                    'error': str(e),
                    'response_time': None
                }
                
                if not optional:
                    pytest.fail(f"Required service {service_name} not accessible: {e}")
                else:
                    print(f"Optional service {service_name} not accessible: {e}")
        
        # Print results summary
        print("\n=== Traefik Service Routing Results ===")
        for service, result in results.items():
            status = "✅" if result.get('accessible') else "❌"
            time_info = f" ({result.get('response_time', 0):.2f}s)" if result.get('response_time') else ""
            print(f"{status} {service}: {result.get('status_code', 'ERROR')}{time_info}")
        
        # Assert at least core services are working
        core_services = ['frontend', 'docs']
        for service in core_services:
            assert results[service]['accessible'], f"Core service {service} must be accessible"
    
    def test_direct_api_access(self, direct_services, session):
        """Test direct API access (not through Traefik)"""
        results = {}
        
        for service_name, config in direct_services.items():
            url = config['url']
            
            try:
                response = session.get(url)
                results[service_name] = {
                    'status_code': response.status_code,
                    'accessible': response.status_code == 200,
                    'content_type': response.headers.get('content-type', ''),
                    'response_time': response.elapsed.total_seconds()
                }
                
                if response.status_code == 200:
                    # Check content
                    content = response.text.lower()
                    expected_content = config['expected_content']
                    content_found = all(term.lower() in content for term in expected_content)
                    results[service_name]['content_match'] = content_found
                
            except requests.exceptions.RequestException as e:
                results[service_name] = {
                    'accessible': False,
                    'error': str(e)
                }
        
        # Print results
        print("\n=== Direct API Access Results ===")
        for service, result in results.items():
            status = "✅" if result.get('accessible') else "❌"
            time_info = f" ({result.get('response_time', 0):.2f}s)" if result.get('response_time') else ""
            print(f"{status} {service}: {result.get('status_code', 'ERROR')}{time_info}")
        
        # Assert API is accessible
        assert results['api']['accessible'], "API must be directly accessible"
    
    def test_api_health_endpoint(self, session):
        """Test API health endpoint specifically"""
        url = 'http://localhost:8000/health'
        
        response = session.get(url)
        assert response.status_code == 200, f"API health endpoint failed: {response.status_code}"
        
        content = response.text.strip()
        assert content == 'healthy', f"Expected 'healthy', got '{content}'"
    
    def test_api_status_endpoint(self, session):
        """Test API status endpoint"""
        url = 'http://localhost:8000/api/v1/status'
        
        response = session.get(url)
        assert response.status_code == 200, f"API status endpoint failed: {response.status_code}"
        
        # Should return JSON
        assert 'application/json' in response.headers.get('content-type', '')
        
        status_data = response.json()
        assert 'system' in status_data, "Status should include system information"
        assert 'vms' in status_data, "Status should include VM information"
    
    def test_cors_headers(self, session):
        """Test CORS headers are present for API"""
        url = 'http://localhost:8000/api/v1/status'
        
        # Test preflight request
        headers = {
            'Origin': 'http://frontend.turdparty.localhost',
            'Access-Control-Request-Method': 'GET'
        }
        
        response = session.options(url, headers=headers)
        
        # Check for CORS headers
        cors_headers = response.headers
        if 'access-control-allow-origin' in cors_headers:
            print(f"CORS enabled: {cors_headers['access-control-allow-origin']}")
        else:
            print("CORS headers not found (might be configured in Traefik)")
    
    def test_websocket_endpoint_availability(self, session):
        """Test WebSocket endpoints are available (HTTP check only)"""
        # We can't test actual WebSocket connections easily in pytest,
        # but we can check if the endpoints exist
        
        base_url = 'http://localhost:8000'
        
        # These should return 426 Upgrade Required for HTTP requests
        websocket_paths = [
            '/api/v1/vms/test-vm/metrics/stream',
            '/api/v1/vms/test-vm/commands/execute',
            '/api/v1/vms/test-vm/files/upload'
        ]
        
        for path in websocket_paths:
            url = urljoin(base_url, path)
            
            try:
                response = session.get(url)
                # WebSocket endpoints should return 426 or 404 for HTTP requests
                assert response.status_code in [404, 426, 400], \
                    f"WebSocket endpoint {path} should reject HTTP requests"
                print(f"WebSocket endpoint {path}: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"WebSocket endpoint {path} error: {e}")
    
    def test_documentation_cross_links(self, session):
        """Test cross-links between documentation and API work"""
        # Get docs page
        docs_response = session.get('http://docs.turdparty.localhost')
        
        if docs_response.status_code == 200:
            content = docs_response.text
            
            # Check for links to API docs
            api_links = [
                'http://localhost:8000/docs',
                'http://localhost:8000/redoc'
            ]
            
            for link in api_links:
                if link in content:
                    # Test the linked endpoint
                    try:
                        link_response = session.get(link, timeout=5)
                        print(f"Cross-link {link}: {link_response.status_code}")
                    except requests.exceptions.RequestException as e:
                        print(f"Cross-link {link} failed: {e}")
        else:
            print(f"Documentation not accessible: {docs_response.status_code}")
    
    def test_service_discovery(self, session):
        """Test service discovery through different access methods"""
        services_to_test = [
            ('docs.turdparty.localhost', 'Documentation'),
            ('frontend.turdparty.localhost', 'Frontend'),
            ('localhost:8000', 'API Direct')
        ]
        
        results = {}
        
        for host, name in services_to_test:
            url = f"http://{host}"
            
            try:
                response = session.get(url, timeout=5)
                results[name] = {
                    'accessible': response.status_code == 200,
                    'status': response.status_code,
                    'time': response.elapsed.total_seconds()
                }
            except requests.exceptions.RequestException as e:
                results[name] = {
                    'accessible': False,
                    'error': str(e)
                }
        
        print("\n=== Service Discovery Results ===")
        for service, result in results.items():
            status = "✅" if result.get('accessible') else "❌"
            time_info = f" ({result.get('time', 0):.2f}s)" if result.get('time') else ""
            print(f"{status} {service}: {result.get('status', 'ERROR')}{time_info}")
        
        # At least API should be accessible
        assert results['API Direct']['accessible'], "API must be accessible directly"
    
    @pytest.mark.slow
    def test_load_balancing_consistency(self, session):
        """Test load balancing consistency (multiple requests)"""
        url = 'http://localhost:8000/health'
        
        responses = []
        for i in range(5):
            try:
                response = session.get(url)
                responses.append({
                    'status': response.status_code,
                    'content': response.text.strip(),
                    'time': response.elapsed.total_seconds()
                })
                time.sleep(0.1)  # Small delay between requests
            except requests.exceptions.RequestException as e:
                responses.append({'error': str(e)})
        
        # All responses should be consistent
        successful_responses = [r for r in responses if 'error' not in r]
        assert len(successful_responses) >= 4, "Most requests should succeed"
        
        # Check consistency
        if successful_responses:
            first_content = successful_responses[0]['content']
            for response in successful_responses:
                assert response['content'] == first_content, \
                    "Load balanced responses should be consistent"
        
        # Check average response time
        times = [r['time'] for r in successful_responses if 'time' in r]
        if times:
            avg_time = sum(times) / len(times)
            print(f"Average response time: {avg_time:.3f}s")
            assert avg_time < 1.0, f"Average response time too high: {avg_time:.3f}s"
    
    def test_error_page_routing(self, session):
        """Test error page routing through Traefik"""
        # Test 404 pages on different services
        services = [
            'http://docs.turdparty.localhost/nonexistent',
            'http://frontend.turdparty.localhost/nonexistent',
            'http://localhost:8000/nonexistent'
        ]
        
        for url in services:
            try:
                response = session.get(url)
                # Should return 404
                assert response.status_code == 404, \
                    f"Non-existent page should return 404: {url}"
                print(f"404 handling OK: {url}")
            except requests.exceptions.RequestException as e:
                print(f"Error testing 404 for {url}: {e}")


if __name__ == "__main__":
    # Run tests with verbose output
    pytest.main([__file__, "-v", "--tb=short"])
