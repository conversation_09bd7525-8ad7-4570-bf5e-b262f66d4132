"""
Integration tests for WebSocket functionality
"""
import pytest
import asyncio
import json
import websockets
import httpx
from typing import List, Dict, Any
import time
import logging

logger = logging.getLogger(__name__)


class TestWebSocketIntegration:
    """Integration tests for WebSocket endpoints"""

    @pytest.fixture
    def api_base_url(self):
        """Base URL for API"""
        return "http://localhost:8000"

    @pytest.fixture
    def ws_base_url(self):
        """Base URL for WebSocket"""
        return "ws://localhost:8000"

    @pytest.fixture
    async def test_vm_id(self, api_base_url):
        """Create a test VM and return its ID"""
        vm_data = {
            "name": f"integration-test-vm-{int(time.time())}",
            "template": "ubuntu:20.04",
            "vm_type": "docker",
            "memory_mb": 512,
            "cpus": 1,
            "domain": "TurdParty",
            "description": "Integration test VM"
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(f"{api_base_url}/api/v1/vms/", json=vm_data)
            if response.status_code == 201:
                vm_info = response.json()
                yield vm_info["vm_id"]
                
                # Cleanup
                try:
                    await client.delete(f"{api_base_url}/api/v1/vms/{vm_info['vm_id']}?force=true")
                except Exception as e:
                    logger.warning(f"Failed to cleanup test VM: {e}")
            else:
                pytest.skip(f"Failed to create test VM: {response.status_code}")

    @pytest.mark.asyncio
    async def test_vm_metrics_streaming_integration(self, ws_base_url, test_vm_id):
        """Test VM metrics streaming end-to-end"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/metrics/stream?vm_type=docker"
        
        metrics_received = []
        
        try:
            async with websockets.connect(uri) as websocket:
                # Collect metrics for 5 seconds
                start_time = time.time()
                while time.time() - start_time < 5:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        data = json.loads(message)
                        metrics_received.append(data)
                        
                        # Validate metrics structure
                        assert "vm_id" in data
                        assert "timestamp" in data
                        assert "cpu_percent" in data
                        assert "memory_percent" in data
                        assert data["vm_id"] == test_vm_id
                        
                        if len(metrics_received) >= 3:
                            break
                            
                    except asyncio.TimeoutError:
                        break
                        
        except Exception as e:
            pytest.fail(f"WebSocket connection failed: {e}")
        
        assert len(metrics_received) >= 1, "Should receive at least one metrics update"
        
        # Verify metrics are updating (timestamps should be different)
        if len(metrics_received) > 1:
            timestamps = [m["timestamp"] for m in metrics_received]
            assert len(set(timestamps)) > 1, "Timestamps should be different for different metrics"

    @pytest.mark.asyncio
    async def test_command_execution_integration(self, ws_base_url, test_vm_id):
        """Test command execution via WebSocket"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/commands/execute"
        
        command_outputs = []
        
        try:
            async with websockets.connect(uri) as websocket:
                # Send a simple command
                command_data = {
                    "command": "echo 'Integration test command'",
                    "working_directory": "/tmp"
                }
                await websocket.send(json.dumps(command_data))
                
                # Collect command output
                command_complete = False
                timeout_count = 0
                
                while not command_complete and timeout_count < 10:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        data = json.loads(message)
                        command_outputs.append(data)
                        
                        # Check if command is complete
                        if data.get("is_complete"):
                            command_complete = True
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                        
        except Exception as e:
            pytest.fail(f"Command execution WebSocket failed: {e}")
        
        assert len(command_outputs) > 0, "Should receive command output"
        
        # Verify command completion
        completed_outputs = [o for o in command_outputs if o.get("is_complete")]
        assert len(completed_outputs) > 0, "Command should complete"
        
        # Verify output contains expected text
        stdout_outputs = [o.get("stdout", "") for o in command_outputs]
        combined_stdout = "".join(stdout_outputs)
        assert "Integration test command" in combined_stdout or "echo" in combined_stdout

    @pytest.mark.asyncio
    async def test_file_upload_websocket_integration(self, ws_base_url, test_vm_id):
        """Test file upload WebSocket integration"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/files/upload"
        
        try:
            async with websockets.connect(uri) as websocket:
                # Send upload initiation
                upload_data = {
                    "target_path": "/tmp/integration_test_file.txt"
                }
                await websocket.send(json.dumps(upload_data))
                
                # Wait for ready response
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                
                assert data.get("type") == "upload_ready"
                
        except Exception as e:
            pytest.fail(f"File upload WebSocket failed: {e}")

    @pytest.mark.asyncio
    async def test_file_watching_integration(self, ws_base_url, test_vm_id):
        """Test file system watching integration"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/files/watch?path=/tmp"
        
        events_received = []
        
        try:
            async with websockets.connect(uri) as websocket:
                # Wait for file system events (mock events)
                start_time = time.time()
                while time.time() - start_time < 10 and len(events_received) < 3:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        data = json.loads(message)
                        events_received.append(data)
                        
                        if data.get("type") == "file_event":
                            assert "vm_id" in data
                            assert "event_type" in data
                            assert "file_path" in data
                            assert "timestamp" in data
                            
                    except asyncio.TimeoutError:
                        break
                        
        except Exception as e:
            pytest.fail(f"File watching WebSocket failed: {e}")
        
        # Should receive at least the start message
        assert len(events_received) > 0

    @pytest.mark.asyncio
    async def test_websocket_connection_management(self, ws_base_url, test_vm_id):
        """Test WebSocket connection management and cleanup"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/metrics/stream"
        
        # Test multiple concurrent connections
        connections = []
        
        try:
            # Open multiple connections
            for i in range(3):
                websocket = await websockets.connect(uri)
                connections.append(websocket)
                
                # Receive one message to ensure connection is working
                message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                data = json.loads(message)
                assert "vm_id" in data
            
            # Close all connections
            for websocket in connections:
                await websocket.close()
                
        except Exception as e:
            # Cleanup any open connections
            for websocket in connections:
                try:
                    await websocket.close()
                except:
                    pass
            pytest.fail(f"Connection management test failed: {e}")

    @pytest.mark.asyncio
    async def test_websocket_error_handling(self, ws_base_url):
        """Test WebSocket error handling with invalid VM"""
        uri = f"{ws_base_url}/api/v1/vms/nonexistent-vm/metrics/stream"
        
        try:
            async with websockets.connect(uri) as websocket:
                # Should receive error or connection should close
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    data = json.loads(message)
                    # If we get a message, it should contain error info
                    assert "error" in data or "status" in data
                except asyncio.TimeoutError:
                    # Timeout is acceptable for error cases
                    pass
                    
        except websockets.exceptions.ConnectionClosedError:
            # Connection closed due to error, which is acceptable
            pass
        except Exception as e:
            # Other connection errors are also acceptable for invalid VMs
            logger.info(f"Expected error for invalid VM: {e}")

    @pytest.mark.asyncio
    async def test_websocket_performance(self, ws_base_url, test_vm_id):
        """Test WebSocket performance characteristics"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/metrics/stream"
        
        metrics_received = []
        start_time = time.time()
        
        try:
            async with websockets.connect(uri) as websocket:
                # Collect metrics for 10 seconds
                while time.time() - start_time < 10:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        data = json.loads(message)
                        metrics_received.append({
                            "timestamp": data.get("timestamp"),
                            "received_at": time.time() * 1000
                        })
                        
                        if len(metrics_received) >= 10:
                            break
                            
                    except asyncio.TimeoutError:
                        break
                        
        except Exception as e:
            pytest.fail(f"Performance test failed: {e}")
        
        if len(metrics_received) >= 2:
            # Calculate average interval between metrics
            intervals = []
            for i in range(1, len(metrics_received)):
                interval = metrics_received[i]["received_at"] - metrics_received[i-1]["received_at"]
                intervals.append(interval)
            
            avg_interval = sum(intervals) / len(intervals)
            
            # Should be approximately 1000ms (1 second) intervals
            assert 800 <= avg_interval <= 1500, f"Average interval {avg_interval}ms should be around 1000ms"

    @pytest.mark.asyncio
    async def test_websocket_reconnection_behavior(self, ws_base_url, test_vm_id):
        """Test WebSocket reconnection behavior"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/metrics/stream"
        
        # First connection
        try:
            async with websockets.connect(uri) as websocket1:
                message1 = await asyncio.wait_for(websocket1.recv(), timeout=5.0)
                data1 = json.loads(message1)
                assert "vm_id" in data1
                
                # Close first connection
                await websocket1.close()
                
            # Second connection (reconnection)
            async with websockets.connect(uri) as websocket2:
                message2 = await asyncio.wait_for(websocket2.recv(), timeout=5.0)
                data2 = json.loads(message2)
                assert "vm_id" in data2
                
                # Should work the same as first connection
                assert data2["vm_id"] == data1["vm_id"]
                
        except Exception as e:
            pytest.fail(f"Reconnection test failed: {e}")


class TestWebSocketStressTest:
    """Stress tests for WebSocket functionality"""

    @pytest.mark.asyncio
    async def test_concurrent_websocket_connections(self, ws_base_url, test_vm_id):
        """Test multiple concurrent WebSocket connections"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/metrics/stream"
        
        async def single_connection_test(connection_id):
            try:
                async with websockets.connect(uri) as websocket:
                    # Receive a few messages
                    for _ in range(3):
                        message = await asyncio.wait_for(websocket.recv(), timeout=5.0)
                        data = json.loads(message)
                        assert "vm_id" in data
                    return True
            except Exception as e:
                logger.error(f"Connection {connection_id} failed: {e}")
                return False
        
        # Run multiple connections concurrently
        tasks = [single_connection_test(i) for i in range(5)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Most connections should succeed
        successful = sum(1 for r in results if r is True)
        assert successful >= 3, f"At least 3 of 5 connections should succeed, got {successful}"

    @pytest.mark.asyncio
    async def test_websocket_message_throughput(self, ws_base_url, test_vm_id):
        """Test WebSocket message throughput"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/metrics/stream"
        
        message_count = 0
        start_time = time.time()
        test_duration = 30  # 30 seconds
        
        try:
            async with websockets.connect(uri) as websocket:
                while time.time() - start_time < test_duration:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=2.0)
                        data = json.loads(message)
                        if "vm_id" in data:
                            message_count += 1
                    except asyncio.TimeoutError:
                        break
                        
        except Exception as e:
            pytest.fail(f"Throughput test failed: {e}")
        
        # Should receive approximately 1 message per second
        expected_messages = test_duration * 0.8  # Allow 20% tolerance
        assert message_count >= expected_messages, f"Expected ~{test_duration} messages, got {message_count}"

    @pytest.mark.asyncio
    async def test_websocket_large_payload_handling(self, ws_base_url, test_vm_id):
        """Test WebSocket handling of large payloads"""
        uri = f"{ws_base_url}/api/v1/vms/{test_vm_id}/commands/execute"
        
        # Send a command that generates large output
        large_command = "find /usr -name '*.so' | head -100"
        
        try:
            async with websockets.connect(uri) as websocket:
                command_data = {
                    "command": large_command,
                    "working_directory": "/tmp"
                }
                await websocket.send(json.dumps(command_data))
                
                total_output_size = 0
                command_complete = False
                timeout_count = 0
                
                while not command_complete and timeout_count < 20:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                        data = json.loads(message)
                        
                        if data.get("stdout"):
                            total_output_size += len(data["stdout"])
                        
                        if data.get("is_complete"):
                            command_complete = True
                            
                    except asyncio.TimeoutError:
                        timeout_count += 1
                
                # Should handle reasonably large output
                assert total_output_size > 0, "Should receive some output"
                
        except Exception as e:
            pytest.fail(f"Large payload test failed: {e}")
