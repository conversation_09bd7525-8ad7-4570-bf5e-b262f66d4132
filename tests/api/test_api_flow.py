"""
Comprehensive API flow tests for TurdParty
Tests the complete workflow from file upload to VM injection
"""
import requests
import json
import time
import logging
from typing import Dict, Any

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
API_BASE_URL = "http://localhost:8000"
API_V1_BASE = f"{API_BASE_URL}/api/v1"

class TestAPIFlow:
    """Test suite for complete API workflow"""
    
    def setup_method(self):
        """Setup method run before each test"""
        self.session = requests.Session()
        self.session.headers.update({
            'Accept': 'application/json'
        })

        # Test data storage
        self.workflow_data = {}
        
    def test_complete_workflow(self):
        """Test the complete workflow: Upload → Start Workflow → Monitor"""
        logger.info("🚀 Starting complete API workflow test...")

        # Step 1: Upload a file
        file_id = self._upload_test_file()
        self.workflow_data['file_id'] = file_id

        # Step 2: Start workflow
        workflow_id = self._start_workflow(file_id)
        self.workflow_data['workflow_id'] = workflow_id

        # Step 3: Monitor the workflow
        self._monitor_workflow(workflow_id)

        # Step 4: Verify the workflow
        self._verify_workflow_completion()

        logger.info("🎉 Complete API workflow test passed!")
    
    def _upload_test_file(self) -> str:
        """Upload a test file and return the file ID"""
        logger.info("📁 Step 1: Uploading test file...")
        
        # Create test malware file content
        test_file_content = b"""#!/bin/bash
# Test malware sample for API flow testing
echo "This is a test malware sample"
echo "File executed at: $(date)" > /tmp/malware_execution.log
echo "Process ID: $$" >> /tmp/malware_execution.log
echo "User: $(whoami)" >> /tmp/malware_execution.log
"""
        
        files = {
            'file': ('test_malware.sh', test_file_content, 'application/x-sh')
        }
        data = {
            'description': 'Test malware sample for complete API workflow testing'
        }
        
        # Upload file
        response = requests.post(
            f"{API_V1_BASE}/files/upload",
            files=files,
            data=data
        )
        
        assert response.status_code == 200, f"File upload failed: {response.text}"
        
        upload_data = response.json()
        file_id = upload_data["file_id"]
        
        # Verify upload (adjust for real API response format)
        assert upload_data["filename"] == "test_malware.sh"
        assert upload_data["status"] == "stored"
        assert "created_at" in upload_data

        logger.info(f"✅ File uploaded successfully - ID: {file_id}")
        return file_id
    
    def _start_workflow(self, file_id: str) -> str:
        """Start a workflow and return the workflow ID"""
        logger.info("🖥️  Step 2: Starting workflow...")

        workflow_data = {
            "file_id": file_id,
            "vm_template": "ubuntu/focal64",
            "vm_memory_mb": 1024,
            "vm_cpus": 1,
            "injection_path": "/tmp/test_malware.sh",
            "description": "Test workflow via API flow test"
        }

        # Remove Content-Type header for form data
        headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}

        response = requests.post(
            f"{API_V1_BASE}/workflow/start",
            data=workflow_data,
            headers=headers
        )

        assert response.status_code == 200, f"Workflow start failed: {response.text}"

        workflow_response = response.json()
        workflow_id = workflow_response["workflow_job_id"]

        # Verify workflow creation
        assert workflow_response["file_id"] == file_id
        assert "status" in workflow_response  # Status can be "pending", "vm_creating", etc.
        assert "created_at" in workflow_response

        logger.info(f"✅ Workflow started successfully - ID: {workflow_id}")
        return workflow_id
    
    def _monitor_workflow(self, workflow_id: str):
        """Monitor the workflow status"""
        logger.info("⚙️  Step 3: Monitoring workflow...")

        response = self.session.get(
            f"{API_V1_BASE}/workflow/{workflow_id}"
        )

        assert response.status_code == 200, f"Workflow monitoring failed: {response.text}"

        workflow_data = response.json()

        # Verify workflow exists and has status
        assert workflow_data["workflow_job_id"] == workflow_id
        assert "status" in workflow_data
        assert "created_at" in workflow_data

        logger.info(f"✅ Workflow monitoring successful - Status: {workflow_data['status']}")
    
    def _verify_workflow_completion(self):
        """Verify the complete workflow"""
        logger.info("🔍 Step 4: Verifying workflow completion...")
        
        file_id = self.workflow_data['file_id']
        workflow_id = self.workflow_data['workflow_id']

        # Verify file still exists
        response = self.session.get(f"{API_V1_BASE}/files/{file_id}")
        assert response.status_code == 200
        file_data = response.json()
        assert file_data["file_id"] == file_id

        # Verify workflow exists and has status
        response = self.session.get(f"{API_V1_BASE}/workflow/{workflow_id}")
        assert response.status_code == 200
        workflow_data = response.json()
        assert workflow_data["workflow_job_id"] == workflow_id
        assert "status" in workflow_data

        # Verify workflow appears in list
        response = self.session.get(f"{API_V1_BASE}/workflow/")
        assert response.status_code == 200
        workflows_list = response.json()
        workflow_ids = [wf["workflow_job_id"] for wf in workflows_list["workflows"]]
        assert workflow_id in workflow_ids
        
        logger.info("✅ Workflow verification completed successfully")
    
    def test_multiple_file_workflow(self):
        """Test workflow with multiple files"""
        logger.info("🔄 Testing multiple file workflow...")
        
        file_ids = []
        workflow_ids = []
        
        # Upload multiple files
        for i in range(3):
            file_content = f"Test file {i+1} content".encode()
            files = {
                'file': (f'test_file_{i+1}.txt', file_content, 'text/plain')
            }
            data = {
                'description': f'Test file {i+1} for multiple file workflow'
            }
            
            response = requests.post(
                f"{API_V1_BASE}/files/upload",
                files=files,
                data=data
            )
            
            assert response.status_code == 200
            file_id = response.json()["file_id"]
            file_ids.append(file_id)
            
            # Create workflow for each file
            workflow_data = {
                "file_id": file_id,
                "vm_template": "debian/bullseye64",
                "vm_memory_mb": 512,
                "vm_cpus": 1,
                "injection_path": f"/tmp/test_file_{i+1}.txt",
                "description": f"Test workflow {i+1} for multiple file test"
            }

            # Remove Content-Type header for form data
            headers = {k: v for k, v in self.session.headers.items() if k.lower() != 'content-type'}

            response = requests.post(
                f"{API_V1_BASE}/workflow/start",
                data=workflow_data,
                headers=headers
            )

            assert response.status_code == 200
            workflow_id = response.json()["workflow_job_id"]
            workflow_ids.append(workflow_id)

        # Verify all files and workflows exist
        assert len(file_ids) == 3
        assert len(workflow_ids) == 3
        
        # Verify files list contains our files
        response = self.session.get(f"{API_V1_BASE}/files/")
        assert response.status_code == 200
        files_list = response.json()
        list_file_ids = [f["file_id"] for f in files_list["files"]]
        
        for file_id in file_ids:
            assert file_id in list_file_ids
        
        logger.info("✅ Multiple file workflow test passed")
    
    def test_error_scenarios(self):
        """Test error scenarios in the workflow"""
        logger.info("⚠️  Testing error scenarios...")
        
        # Test creating injection with non-existent file
        fake_file_id = "00000000-0000-0000-0000-000000000000"
        injection_data = {
            "file_upload_id": fake_file_id,
            "template_id": "ubuntu_2204",
            "target_path": "/tmp/fake_file.txt",
            "permissions": "0755"
        }
        
        response = self.session.post(
            f"{API_V1_BASE}/virtual-machines/injections/",
            json=injection_data
        )
        
        # Should still create injection (validation happens during processing)
        assert response.status_code == 201
        
        # Test processing non-existent injection
        fake_injection_id = "00000000-0000-0000-0000-000000000000"
        response = self.session.post(
            f"{API_V1_BASE}/virtual-machines/injections/{fake_injection_id}/process"
        )
        
        assert response.status_code == 404
        
        # Test invalid injection data
        invalid_data = {
            "file_upload_id": "invalid-id",
            "template_id": "",
            "target_path": "",
            "permissions": "invalid"
        }
        
        response = self.session.post(
            f"{API_V1_BASE}/virtual-machines/injections/",
            json=invalid_data
        )
        
        # Should still create (validation is minimal in mock implementation)
        assert response.status_code == 201
        
        logger.info("✅ Error scenarios test passed")
    
    def test_template_injection_flow(self):
        """Test template injection workflow"""
        logger.info("📋 Testing template injection workflow...")
        
        # Upload file
        file_content = b"Template injection test content"
        files = {
            'file': ('template_test.txt', file_content, 'text/plain')
        }
        data = {
            'description': 'Template injection test file'
        }
        
        response = requests.post(
            f"{API_V1_BASE}/files/upload",
            files=files,
            data=data
        )
        
        assert response.status_code == 201
        file_id = response.json()["file_id"]
        
        # Create template injection
        injection_data = {
            "file_upload_id": file_id,
            "template_id": "windows_10",
            "target_path": "C:\\temp\\template_test.txt",
            "permissions": "0644"
        }
        
        response = self.session.post(
            f"{API_V1_BASE}/template_injection/",
            json=injection_data
        )
        
        assert response.status_code == 201
        injection_id = response.json()["injection_id"]
        
        # Process template injection
        response = self.session.post(
            f"{API_V1_BASE}/template_injection/{injection_id}/process"
        )
        
        assert response.status_code == 200
        
        logger.info("✅ Template injection workflow test passed")

if __name__ == "__main__":
    # Run tests directly
    test_suite = TestAPIFlow()
    test_suite.setup_method()
    
    try:
        test_suite.test_complete_workflow()
        test_suite.test_multiple_file_workflow()
        test_suite.test_error_scenarios()
        test_suite.test_template_injection_flow()
        
        print("\n🎉 All API flow tests passed!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise
