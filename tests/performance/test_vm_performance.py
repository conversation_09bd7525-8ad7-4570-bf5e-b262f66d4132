"""
Performance and benchmark tests for VM management
"""
import pytest
import asyncio
import time
import statistics
from typing import List, Dict, Any
import httpx
import websockets
import json
import concurrent.futures
import threading


class TestVMPerformanceBenchmarks:
    """Performance benchmarks for VM operations"""

    @pytest.fixture
    def api_base_url(self):
        """Base URL for API"""
        return "http://localhost:8000"

    @pytest.fixture
    def ws_base_url(self):
        """Base URL for WebSocket"""
        return "ws://localhost:8000"

    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_vm_creation_performance(self, api_base_url, benchmark):
        """Benchmark VM creation performance"""
        
        async def create_vm():
            vm_data = {
                "name": f"perf-test-vm-{int(time.time() * 1000)}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 512,
                "cpus": 1,
                "domain": "TurdParty"
            }
            
            async with httpx.AsyncClient() as client:
                start_time = time.time()
                response = await client.post(f"{api_base_url}/api/v1/vms/", json=vm_data)
                end_time = time.time()
                
                if response.status_code == 201:
                    vm_id = response.json()["vm_id"]
                    # Cleanup
                    try:
                        await client.delete(f"{api_base_url}/api/v1/vms/{vm_id}?force=true")
                    except:
                        pass
                
                return end_time - start_time
        
        # Benchmark the VM creation
        result = benchmark(lambda: asyncio.run(create_vm()))
        
        # Assert performance requirements
        assert result < 5.0, f"VM creation took {result:.2f}s, should be under 5s"

    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_websocket_connection_performance(self, ws_base_url, benchmark):
        """Benchmark WebSocket connection establishment"""
        
        async def connect_websocket():
            uri = f"{ws_base_url}/api/v1/vms/test-vm/metrics/stream"
            
            start_time = time.time()
            try:
                async with websockets.connect(uri) as websocket:
                    # Wait for first message to ensure connection is fully established
                    await asyncio.wait_for(websocket.recv(), timeout=2.0)
                end_time = time.time()
                return end_time - start_time
            except:
                end_time = time.time()
                return end_time - start_time
        
        result = benchmark(lambda: asyncio.run(connect_websocket()))
        
        # WebSocket connection should be fast
        assert result < 1.0, f"WebSocket connection took {result:.2f}s, should be under 1s"

    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_metrics_collection_performance(self, benchmark):
        """Benchmark metrics collection performance"""
        from api.services.vm_metrics_service import vm_metrics_service
        
        async def collect_metrics():
            await vm_metrics_service.initialize()
            start_time = time.time()
            metrics = await vm_metrics_service.get_vm_metrics("test-vm", "docker")
            end_time = time.time()
            return end_time - start_time
        
        result = benchmark(lambda: asyncio.run(collect_metrics()))
        
        # Metrics collection should be very fast
        assert result < 0.5, f"Metrics collection took {result:.2f}s, should be under 0.5s"

    @pytest.mark.benchmark
    def test_concurrent_vm_operations_performance(self, api_base_url, benchmark):
        """Benchmark concurrent VM operations"""
        
        def concurrent_vm_operations():
            def create_and_delete_vm(vm_index):
                import httpx
                
                vm_data = {
                    "name": f"concurrent-vm-{vm_index}-{int(time.time() * 1000)}",
                    "template": "ubuntu:20.04",
                    "vm_type": "docker",
                    "memory_mb": 512,
                    "cpus": 1,
                    "domain": "TurdParty"
                }
                
                start_time = time.time()
                
                with httpx.Client() as client:
                    # Create VM
                    response = client.post(f"{api_base_url}/api/v1/vms/", json=vm_data)
                    if response.status_code == 201:
                        vm_id = response.json()["vm_id"]
                        
                        # Delete VM
                        client.delete(f"{api_base_url}/api/v1/vms/{vm_id}?force=true")
                
                end_time = time.time()
                return end_time - start_time
            
            # Run 5 concurrent operations
            with concurrent.futures.ThreadPoolExecutor(max_workers=5) as executor:
                futures = [executor.submit(create_and_delete_vm, i) for i in range(5)]
                results = [future.result() for future in concurrent.futures.as_completed(futures)]
            
            return max(results)  # Return the longest operation time
        
        result = benchmark(concurrent_vm_operations)
        
        # Concurrent operations should complete reasonably fast
        assert result < 10.0, f"Concurrent operations took {result:.2f}s, should be under 10s"

    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_websocket_throughput_performance(self, ws_base_url):
        """Test WebSocket message throughput"""
        uri = f"{ws_base_url}/api/v1/vms/test-vm/metrics/stream"
        
        message_count = 0
        start_time = time.time()
        test_duration = 10  # 10 seconds
        
        try:
            async with websockets.connect(uri) as websocket:
                while time.time() - start_time < test_duration:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=1.0)
                        data = json.loads(message)
                        if "vm_id" in data:
                            message_count += 1
                    except asyncio.TimeoutError:
                        break
        except:
            pass
        
        throughput = message_count / test_duration
        
        # Should achieve at least 0.8 messages per second (allowing for some variance)
        assert throughput >= 0.8, f"Throughput {throughput:.2f} msg/s should be >= 0.8 msg/s"

    @pytest.mark.benchmark
    def test_api_response_time_distribution(self, api_base_url):
        """Test API response time distribution"""
        response_times = []
        
        def measure_response_time():
            import httpx
            
            with httpx.Client() as client:
                start_time = time.time()
                response = client.get(f"{api_base_url}/api/v1/vms/templates")
                end_time = time.time()
                
                if response.status_code == 200:
                    return end_time - start_time
                return None
        
        # Collect 20 response time samples
        for _ in range(20):
            response_time = measure_response_time()
            if response_time is not None:
                response_times.append(response_time)
            time.sleep(0.1)  # Small delay between requests
        
        if response_times:
            avg_time = statistics.mean(response_times)
            p95_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            
            # Performance assertions
            assert avg_time < 0.5, f"Average response time {avg_time:.3f}s should be under 0.5s"
            assert p95_time < 1.0, f"95th percentile {p95_time:.3f}s should be under 1.0s"

    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_memory_usage_during_operations(self, api_base_url):
        """Test memory usage during VM operations"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        # Perform multiple VM operations
        async with httpx.AsyncClient() as client:
            vm_ids = []
            
            # Create 5 VMs
            for i in range(5):
                vm_data = {
                    "name": f"memory-test-vm-{i}",
                    "template": "ubuntu:20.04",
                    "vm_type": "docker",
                    "memory_mb": 512,
                    "cpus": 1,
                    "domain": "TurdParty"
                }
                
                response = await client.post(f"{api_base_url}/api/v1/vms/", json=vm_data)
                if response.status_code == 201:
                    vm_ids.append(response.json()["vm_id"])
            
            # Check memory after creation
            mid_memory = process.memory_info().rss
            
            # Delete all VMs
            for vm_id in vm_ids:
                try:
                    await client.delete(f"{api_base_url}/api/v1/vms/{vm_id}?force=true")
                except:
                    pass
            
            # Check memory after cleanup
            final_memory = process.memory_info().rss
        
        # Memory usage should not grow excessively
        memory_growth = (mid_memory - initial_memory) / (1024 * 1024)  # MB
        memory_cleanup = (final_memory - initial_memory) / (1024 * 1024)  # MB
        
        assert memory_growth < 100, f"Memory growth {memory_growth:.1f}MB should be under 100MB"
        assert memory_cleanup < 50, f"Memory after cleanup {memory_cleanup:.1f}MB should be under 50MB"


class TestVMScalabilityTests:
    """Scalability tests for VM management"""

    @pytest.fixture
    def api_base_url(self):
        return "http://localhost:8000"

    @pytest.mark.scalability
    @pytest.mark.asyncio
    async def test_multiple_vm_creation_scalability(self, api_base_url):
        """Test creating multiple VMs simultaneously"""
        vm_count = 10
        creation_times = []
        vm_ids = []
        
        async def create_vm(vm_index):
            vm_data = {
                "name": f"scale-test-vm-{vm_index}",
                "template": "ubuntu:20.04",
                "vm_type": "docker",
                "memory_mb": 512,
                "cpus": 1,
                "domain": "TurdParty"
            }
            
            start_time = time.time()
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{api_base_url}/api/v1/vms/", json=vm_data)
                end_time = time.time()
                
                if response.status_code == 201:
                    return response.json()["vm_id"], end_time - start_time
                return None, end_time - start_time
        
        # Create VMs concurrently
        tasks = [create_vm(i) for i in range(vm_count)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Collect results
        for result in results:
            if isinstance(result, tuple):
                vm_id, creation_time = result
                if vm_id:
                    vm_ids.append(vm_id)
                creation_times.append(creation_time)
        
        # Cleanup
        async with httpx.AsyncClient() as client:
            for vm_id in vm_ids:
                try:
                    await client.delete(f"{api_base_url}/api/v1/vms/{vm_id}?force=true")
                except:
                    pass
        
        # Scalability assertions
        successful_creations = len(vm_ids)
        avg_creation_time = statistics.mean(creation_times) if creation_times else 0
        
        assert successful_creations >= vm_count * 0.8, f"Should create at least 80% of VMs successfully"
        assert avg_creation_time < 10.0, f"Average creation time {avg_creation_time:.2f}s should be under 10s"

    @pytest.mark.scalability
    @pytest.mark.asyncio
    async def test_websocket_connection_scalability(self, ws_base_url):
        """Test multiple WebSocket connections"""
        connection_count = 20
        successful_connections = 0
        
        async def test_connection(connection_id):
            try:
                uri = f"{ws_base_url}/api/v1/vms/scale-test-vm/metrics/stream"
                async with websockets.connect(uri) as websocket:
                    # Receive one message to verify connection
                    await asyncio.wait_for(websocket.recv(), timeout=5.0)
                    return True
            except:
                return False
        
        # Create connections concurrently
        tasks = [test_connection(i) for i in range(connection_count)]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        successful_connections = sum(1 for r in results if r is True)
        
        # Should handle most connections successfully
        success_rate = successful_connections / connection_count
        assert success_rate >= 0.7, f"Success rate {success_rate:.2f} should be at least 70%"

    @pytest.mark.scalability
    def test_api_load_handling(self, api_base_url):
        """Test API load handling with many requests"""
        request_count = 50
        response_times = []
        success_count = 0
        
        def make_request(request_id):
            import httpx
            
            start_time = time.time()
            try:
                with httpx.Client() as client:
                    response = client.get(f"{api_base_url}/api/v1/vms/templates")
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        return True, end_time - start_time
                    return False, end_time - start_time
            except:
                end_time = time.time()
                return False, end_time - start_time
        
        # Make requests concurrently
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request, i) for i in range(request_count)]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        
        # Analyze results
        for success, response_time in results:
            if success:
                success_count += 1
            response_times.append(response_time)
        
        success_rate = success_count / request_count
        avg_response_time = statistics.mean(response_times)
        
        # Load handling assertions
        assert success_rate >= 0.9, f"Success rate {success_rate:.2f} should be at least 90%"
        assert avg_response_time < 2.0, f"Average response time {avg_response_time:.2f}s should be under 2s"


class TestVMResourceUtilization:
    """Test resource utilization during VM operations"""

    @pytest.mark.resource
    def test_cpu_utilization_during_operations(self):
        """Test CPU utilization during VM operations"""
        import psutil
        
        # Monitor CPU for 30 seconds during operations
        cpu_samples = []
        start_time = time.time()
        
        while time.time() - start_time < 30:
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_samples.append(cpu_percent)
        
        avg_cpu = statistics.mean(cpu_samples)
        max_cpu = max(cpu_samples)
        
        # CPU utilization should be reasonable
        assert avg_cpu < 80, f"Average CPU {avg_cpu:.1f}% should be under 80%"
        assert max_cpu < 95, f"Max CPU {max_cpu:.1f}% should be under 95%"

    @pytest.mark.resource
    def test_memory_leak_detection(self, api_base_url):
        """Test for memory leaks during repeated operations"""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        memory_samples = [initial_memory]
        
        # Perform repeated operations
        for cycle in range(10):
            # Simulate VM operations
            import httpx
            
            with httpx.Client() as client:
                # Create and delete VM
                vm_data = {
                    "name": f"leak-test-vm-{cycle}",
                    "template": "ubuntu:20.04",
                    "vm_type": "docker",
                    "memory_mb": 512,
                    "cpus": 1,
                    "domain": "TurdParty"
                }
                
                response = client.post(f"{api_base_url}/api/v1/vms/", json=vm_data)
                if response.status_code == 201:
                    vm_id = response.json()["vm_id"]
                    client.delete(f"{api_base_url}/api/v1/vms/{vm_id}?force=true")
            
            # Sample memory
            current_memory = process.memory_info().rss
            memory_samples.append(current_memory)
            
            time.sleep(1)  # Brief pause between cycles
        
        # Analyze memory trend
        memory_growth = (memory_samples[-1] - memory_samples[0]) / (1024 * 1024)  # MB
        
        # Should not have significant memory growth
        assert memory_growth < 50, f"Memory growth {memory_growth:.1f}MB should be under 50MB"
