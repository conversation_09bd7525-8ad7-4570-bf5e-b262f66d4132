"""
Performance benchmarks using pytest-benchmark for TurdParty application.

Benchmarks critical performance paths and regression testing.
Ensures PEP8, PEP257, and PEP484 compliance.
"""

import hashlib
import json
from typing import Any

import pytest

from api.models.file_injection import ELKLogEntry, FileInjectionCreate


class TestModelPerformance:
    """Benchmark tests for Pydantic model performance."""

    @pytest.mark.benchmark
    def test_file_injection_create_performance(self, benchmark) -> None:
        """Benchmark FileInjectionCreate model creation."""
        data = {
            "filename": "performance_test.sh",
            "target_path": "/app/scripts/performance_test.sh",
            "permissions": "0755",
            "description": "Performance benchmark test file"
        }

        result = benchmark(FileInjectionCreate, **data)

        assert result.filename == "performance_test.sh"
        assert result.permissions == "0755"

    @pytest.mark.benchmark
    def test_file_injection_serialization_performance(self, benchmark) -> None:
        """Benchmark FileInjectionCreate serialization."""
        model = FileInjectionCreate(
            filename="benchmark.sh",
            target_path="/app/benchmark.sh",
            permissions="0755",
            description="Serialization benchmark"
        )

        result = benchmark(model.model_dump_json)

        # Verify result is valid JSON
        parsed = json.loads(result)
        assert parsed["filename"] == "benchmark.sh"

    @pytest.mark.benchmark
    def test_file_injection_deserialization_performance(self, benchmark) -> None:
        """Benchmark FileInjectionCreate deserialization."""
        json_data = '{"filename": "test.sh", "target_path": "/app/test.sh", "permissions": "0755"}'

        result = benchmark(FileInjectionCreate.model_validate_json, json_data)

        assert result.filename == "test.sh"

    @pytest.mark.benchmark
    def test_elk_log_entry_creation_performance(self, benchmark) -> None:
        """Benchmark ELKLogEntry model creation."""
        data = {
            "event_type": "file_injection_created",
            "message": "File injection created successfully",
            "injection_id": "benchmark-test-123",
            "details": {
                "filename": "benchmark.sh",
                "size": 1024,
                "hash": "abc123def456"
            }
        }

        result = benchmark(ELKLogEntry, **data)

        assert result.event_type == "file_injection_created"
        assert result.injection_id == "benchmark-test-123"


class TestHashingPerformance:
    """Benchmark tests for hashing operations."""

    @pytest.mark.benchmark
    @pytest.mark.parametrize("size", [1024, 10240, 102400, 1048576])  # 1KB, 10KB, 100KB, 1MB
    def test_sha256_hashing_performance(self, benchmark, size: int) -> None:
        """Benchmark SHA256 hashing for different file sizes."""
        test_data = b"A" * size

        result = benchmark(hashlib.sha256, test_data)

        # Verify hash is correct length
        assert len(result.hexdigest()) == 64

    @pytest.mark.benchmark
    def test_hash_comparison_performance(self, benchmark) -> None:
        """Benchmark hash comparison operations."""
        hash1 = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"
        hash2 = "a665a45920422f9d417e4867efdc4fb8a04a1f3fff1fa07e998e86f7f7a27ae3"

        result = benchmark(lambda: hash1 == hash2)

        assert result is True


class TestDataProcessingPerformance:
    """Benchmark tests for data processing operations."""

    @pytest.mark.benchmark
    def test_dictionary_processing_performance(self, benchmark) -> None:
        """Benchmark dictionary processing operations."""
        def process_injection_data() -> dict[str, Any]:
            """Process injection data dictionary."""
            data = {
                "id": "benchmark-test",
                "filename": "test.sh",
                "target_path": "/app/test.sh",
                "permissions": "0755",
                "status": "pending",
                "metadata": {
                    "size": 1024,
                    "hash": "abc123",
                    "created": "2024-01-01T10:00:00Z"
                }
            }

            # Simulate processing
            processed = {
                "injection_id": data["id"],
                "file_info": {
                    "name": data["filename"],
                    "path": data["target_path"],
                    "perms": data["permissions"]
                },
                "status_info": {
                    "current": data["status"],
                    "size": data["metadata"]["size"],
                    "checksum": data["metadata"]["hash"]
                }
            }

            return processed

        result = benchmark(process_injection_data)

        assert result["injection_id"] == "benchmark-test"
        assert result["file_info"]["name"] == "test.sh"

    @pytest.mark.benchmark
    def test_list_processing_performance(self, benchmark) -> None:
        """Benchmark list processing operations."""
        def process_injection_list() -> list[dict[str, Any]]:
            """Process list of injections."""
            injections = [
                {"id": f"injection-{i}", "status": "pending", "priority": i % 3}
                for i in range(100)
            ]

            # Filter and sort
            pending = [inj for inj in injections if inj["status"] == "pending"]
            sorted_pending = sorted(pending, key=lambda x: x["priority"])

            return sorted_pending

        result = benchmark(process_injection_list)

        assert len(result) == 100
        assert all(inj["status"] == "pending" for inj in result)

    @pytest.mark.benchmark
    def test_json_processing_performance(self, benchmark) -> None:
        """Benchmark JSON processing operations."""
        def process_json_data() -> dict[str, Any]:
            """Process JSON data."""
            data = {
                "injections": [
                    {
                        "id": f"inj-{i}",
                        "filename": f"file-{i}.sh",
                        "status": "pending" if i % 2 == 0 else "completed",
                        "metadata": {
                            "size": 1024 * i,
                            "created": f"2024-01-{i:02d}T10:00:00Z"
                        }
                    }
                    for i in range(1, 21)
                ]
            }

            # Serialize and deserialize
            json_str = json.dumps(data)
            parsed = json.loads(json_str)

            # Process
            pending_count = sum(
                1 for inj in parsed["injections"]
                if inj["status"] == "pending"
            )

            return {
                "total": len(parsed["injections"]),
                "pending": pending_count,
                "json_size": len(json_str)
            }

        result = benchmark(process_json_data)

        assert result["total"] == 20
        assert result["pending"] == 10


class TestConcurrencyPerformance:
    """Benchmark tests for concurrency scenarios."""

    @pytest.mark.benchmark
    @pytest.mark.asyncio
    async def test_async_operation_performance(self, benchmark) -> None:
        """Benchmark async operation performance."""
        import asyncio

        async def async_processing() -> dict[str, Any]:
            """Simulate async processing."""
            # Simulate multiple async operations
            tasks = []
            for i in range(10):
                async def process_item(item_id: int) -> dict[str, Any]:
                    # Simulate async work
                    await asyncio.sleep(0.001)  # 1ms delay
                    return {
                        "id": item_id,
                        "processed": True,
                        "result": f"result-{item_id}"
                    }

                tasks.append(process_item(i))

            results = await asyncio.gather(*tasks)

            return {
                "processed_count": len(results),
                "all_successful": all(r["processed"] for r in results)
            }

        result = await benchmark(async_processing)

        assert result["processed_count"] == 10
        assert result["all_successful"] is True

    @pytest.mark.benchmark
    def test_concurrent_model_creation_performance(self, benchmark) -> None:
        """Benchmark concurrent model creation."""
        def create_multiple_models() -> list[FileInjectionCreate]:
            """Create multiple models concurrently."""
            models = []
            for i in range(50):
                model = FileInjectionCreate(
                    filename=f"concurrent-{i}.sh",
                    target_path=f"/app/concurrent-{i}.sh",
                    permissions="0755",
                    description=f"Concurrent test file {i}"
                )
                models.append(model)

            return models

        result = benchmark(create_multiple_models)

        assert len(result) == 50
        assert all(model.permissions == "0755" for model in result)


class TestMemoryPerformance:
    """Benchmark tests for memory usage patterns."""

    @pytest.mark.benchmark
    def test_large_data_processing_performance(self, benchmark) -> None:
        """Benchmark processing of large data structures."""
        def process_large_dataset() -> dict[str, Any]:
            """Process large dataset."""
            # Create large dataset
            dataset = {
                f"injection-{i}": {
                    "filename": f"file-{i}.sh",
                    "content": "A" * 1000,  # 1KB per file
                    "metadata": {
                        "size": 1000,
                        "hash": hashlib.sha256(f"file-{i}".encode()).hexdigest(),
                        "tags": [f"tag-{j}" for j in range(5)]
                    }
                }
                for i in range(100)  # 100 files = ~100KB total
            }

            # Process dataset
            total_size = sum(
                len(data["content"])
                for data in dataset.values()
            )

            file_count = len(dataset)

            # Calculate some statistics
            avg_size = total_size / file_count if file_count > 0 else 0

            return {
                "file_count": file_count,
                "total_size": total_size,
                "average_size": avg_size,
                "processed": True
            }

        result = benchmark(process_large_dataset)

        assert result["file_count"] == 100
        assert result["total_size"] == 100000  # 100KB
        assert result["average_size"] == 1000.0

    @pytest.mark.benchmark
    def test_memory_cleanup_performance(self, benchmark) -> None:
        """Benchmark memory cleanup operations."""
        def create_and_cleanup() -> bool:
            """Create objects and clean them up."""
            # Create objects
            objects = []
            for i in range(1000):
                obj = {
                    "id": i,
                    "data": "x" * 100,  # 100 bytes per object
                    "metadata": {"created": f"2024-01-01T{i:02d}:00:00Z"}
                }
                objects.append(obj)

            # Process objects
            processed_count = len(objects)

            # Clear objects (simulate cleanup)
            objects.clear()

            return processed_count == 1000

        result = benchmark(create_and_cleanup)

        assert result is True
