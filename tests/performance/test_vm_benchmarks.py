"""
VM Performance Benchmarks for TurdParty
Tests VM startup times, interactions, and resource usage
"""
import time
import json
import logging
import statistics
import requests
from datetime import datetime
from typing import Dict, List, Any
import subprocess
import psutil

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test configuration
API_BASE_URL = "http://localhost:8000"
API_V1_BASE = f"{API_BASE_URL}/api/v1"

class VMBenchmarkTests:
    """VM performance benchmark test suite"""
    
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        # Benchmark results storage
        self.benchmark_results = {
            'vm_startup_times': [],
            'vm_interaction_times': [],
            'vm_resource_usage': [],
            'vm_cleanup_times': [],
            'test_metadata': {
                'timestamp': datetime.utcnow().isoformat(),
                'system_info': self._get_system_info()
            }
        }
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information for benchmark context"""
        return {
            'cpu_count': psutil.cpu_count(),
            'memory_total_gb': round(psutil.virtual_memory().total / (1024**3), 2),
            'disk_usage_gb': round(psutil.disk_usage('/').total / (1024**3), 2),
            'platform': subprocess.check_output(['uname', '-a']).decode().strip()
        }
    
    def benchmark_vm_startup_times(self, iterations: int = 5):
        """Benchmark VM startup times across different templates"""
        logger.info(f"🚀 Benchmarking VM startup times ({iterations} iterations)...")
        
        # Test different VM templates
        templates = [
            {"template": "ubuntu/focal64", "vm_type": "docker"},
            {"template": "alpine:latest", "vm_type": "docker"},
            {"template": "ubuntu:20.04", "vm_type": "docker"}
        ]
        
        for template_config in templates:
            template_times = []
            
            for i in range(iterations):
                logger.info(f"Testing {template_config['template']} - iteration {i+1}/{iterations}")
                
                # Create VM and measure startup time
                start_time = time.time()
                vm_id = self._create_test_vm(template_config)
                
                if vm_id:
                    # Wait for VM to be running
                    startup_time = self._wait_for_vm_running(vm_id, start_time)
                    template_times.append(startup_time)
                    
                    # Cleanup
                    self._cleanup_vm(vm_id)
                    
                    # Wait between iterations
                    time.sleep(2)
                else:
                    logger.warning(f"Failed to create VM for {template_config['template']}")
            
            if template_times:
                avg_time = statistics.mean(template_times)
                min_time = min(template_times)
                max_time = max(template_times)
                
                result = {
                    'template': template_config['template'],
                    'vm_type': template_config['vm_type'],
                    'iterations': len(template_times),
                    'avg_startup_time': avg_time,
                    'min_startup_time': min_time,
                    'max_startup_time': max_time,
                    'all_times': template_times
                }
                
                self.benchmark_results['vm_startup_times'].append(result)
                
                logger.info(f"✅ {template_config['template']}: Avg={avg_time:.2f}s, Min={min_time:.2f}s, Max={max_time:.2f}s")
    
    def benchmark_vm_interactions(self, iterations: int = 3):
        """Benchmark VM interaction performance (actions, status checks)"""
        logger.info(f"🔄 Benchmarking VM interactions ({iterations} iterations)...")
        
        # Create a test VM for interactions
        vm_config = {"template": "ubuntu/focal64", "vm_type": "docker"}
        vm_id = self._create_test_vm(vm_config)
        
        if not vm_id:
            logger.error("Failed to create VM for interaction testing")
            return
        
        # Wait for VM to be ready
        self._wait_for_vm_running(vm_id, time.time())
        
        interaction_times = {
            'status_check': [],
            'vm_action_stop': [],
            'vm_action_start': [],
            'vm_details': []
        }
        
        for i in range(iterations):
            logger.info(f"Interaction test iteration {i+1}/{iterations}")
            
            # Test status check performance
            start_time = time.time()
            response = self.session.get(f"{API_V1_BASE}/vms/{vm_id}")
            if response.status_code == 200:
                interaction_times['status_check'].append(time.time() - start_time)
            
            # Test VM action performance (stop)
            start_time = time.time()
            response = self.session.post(f"{API_V1_BASE}/vms/{vm_id}/action", 
                                       json={"action": "stop"})
            if response.status_code == 200:
                interaction_times['vm_action_stop'].append(time.time() - start_time)
            
            time.sleep(2)  # Wait for action to complete
            
            # Test VM action performance (start)
            start_time = time.time()
            response = self.session.post(f"{API_V1_BASE}/vms/{vm_id}/action", 
                                       json={"action": "start"})
            if response.status_code == 200:
                interaction_times['vm_action_start'].append(time.time() - start_time)
            
            time.sleep(2)  # Wait for action to complete
            
            # Test detailed VM info
            start_time = time.time()
            response = self.session.get(f"{API_V1_BASE}/vms/{vm_id}")
            if response.status_code == 200:
                interaction_times['vm_details'].append(time.time() - start_time)
        
        # Calculate statistics
        for interaction_type, times in interaction_times.items():
            if times:
                result = {
                    'interaction_type': interaction_type,
                    'iterations': len(times),
                    'avg_time': statistics.mean(times),
                    'min_time': min(times),
                    'max_time': max(times),
                    'all_times': times
                }
                self.benchmark_results['vm_interaction_times'].append(result)
                
                logger.info(f"✅ {interaction_type}: Avg={result['avg_time']:.3f}s")
        
        # Cleanup
        self._cleanup_vm(vm_id)
    
    def benchmark_vm_resource_usage(self):
        """Benchmark VM resource usage and system impact"""
        logger.info("📊 Benchmarking VM resource usage...")
        
        # Get baseline system metrics
        baseline_cpu = psutil.cpu_percent(interval=1)
        baseline_memory = psutil.virtual_memory().percent
        
        # Create multiple VMs and measure resource impact
        vm_ids = []
        vm_counts = [1, 2, 3]  # Test with different VM counts
        
        for vm_count in vm_counts:
            logger.info(f"Testing with {vm_count} VMs...")
            
            # Create VMs
            start_time = time.time()
            for i in range(vm_count):
                vm_config = {
                    "template": "alpine:latest", 
                    "vm_type": "docker",
                    "memory_mb": 256,  # Smaller VMs for resource testing
                    "cpus": 1
                }
                vm_id = self._create_test_vm(vm_config)
                if vm_id:
                    vm_ids.append(vm_id)
            
            # Wait for all VMs to be running
            for vm_id in vm_ids[-vm_count:]:
                self._wait_for_vm_running(vm_id, start_time)
            
            # Measure resource usage
            time.sleep(5)  # Let system stabilize
            cpu_usage = psutil.cpu_percent(interval=2)
            memory_usage = psutil.virtual_memory().percent
            
            result = {
                'vm_count': vm_count,
                'cpu_usage_percent': cpu_usage,
                'memory_usage_percent': memory_usage,
                'cpu_increase': cpu_usage - baseline_cpu,
                'memory_increase': memory_usage - baseline_memory,
                'creation_time': time.time() - start_time
            }
            
            self.benchmark_results['vm_resource_usage'].append(result)
            
            logger.info(f"✅ {vm_count} VMs: CPU={cpu_usage:.1f}% (+{result['cpu_increase']:.1f}%), "
                       f"Memory={memory_usage:.1f}% (+{result['memory_increase']:.1f}%)")
        
        # Cleanup all VMs
        for vm_id in vm_ids:
            self._cleanup_vm(vm_id)
    
    def benchmark_vm_cleanup_times(self, iterations: int = 5):
        """Benchmark VM cleanup/deletion times"""
        logger.info(f"🗑️ Benchmarking VM cleanup times ({iterations} iterations)...")
        
        cleanup_times = []
        
        for i in range(iterations):
            # Create VM
            vm_config = {"template": "alpine:latest", "vm_type": "docker"}
            vm_id = self._create_test_vm(vm_config)
            
            if vm_id:
                # Wait for VM to be ready
                self._wait_for_vm_running(vm_id, time.time())
                
                # Measure cleanup time
                start_time = time.time()
                success = self._cleanup_vm(vm_id)
                
                if success:
                    cleanup_time = time.time() - start_time
                    cleanup_times.append(cleanup_time)
                    logger.info(f"Cleanup iteration {i+1}: {cleanup_time:.2f}s")
                
                time.sleep(1)  # Brief pause between iterations
        
        if cleanup_times:
            result = {
                'iterations': len(cleanup_times),
                'avg_cleanup_time': statistics.mean(cleanup_times),
                'min_cleanup_time': min(cleanup_times),
                'max_cleanup_time': max(cleanup_times),
                'all_times': cleanup_times
            }
            
            self.benchmark_results['vm_cleanup_times'].append(result)
            
            logger.info(f"✅ Cleanup: Avg={result['avg_cleanup_time']:.2f}s, "
                       f"Min={result['min_cleanup_time']:.2f}s, Max={result['max_cleanup_time']:.2f}s")
    
    def _create_test_vm(self, config: Dict[str, Any]) -> str:
        """Create a test VM and return its ID"""
        vm_data = {
            "name": f"benchmark-vm-{int(time.time())}-{hash(str(config)) % 1000}",
            "template": config["template"],
            "vm_type": config["vm_type"],
            "memory_mb": config.get("memory_mb", 512),
            "cpus": config.get("cpus", 1),
            "description": "VM created for benchmark testing"
        }
        
        try:
            response = self.session.post(f"{API_V1_BASE}/vms/", json=vm_data)
            if response.status_code == 201:
                return response.json()["vm_id"]
        except Exception as e:
            logger.error(f"Failed to create VM: {e}")
        
        return None
    
    def _wait_for_vm_running(self, vm_id: str, start_time: float, timeout: int = 60) -> float:
        """Wait for VM to be running and return startup time"""
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(f"{API_V1_BASE}/vms/{vm_id}")
                if response.status_code == 200:
                    vm_data = response.json()
                    if vm_data.get("status") == "running":
                        return time.time() - start_time
            except Exception:
                pass
            
            time.sleep(0.5)
        
        logger.warning(f"VM {vm_id} did not start within {timeout}s")
        return timeout
    
    def _cleanup_vm(self, vm_id: str) -> bool:
        """Delete a VM and return success status"""
        try:
            response = self.session.delete(f"{API_V1_BASE}/vms/{vm_id}")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Failed to cleanup VM {vm_id}: {e}")
            return False
    
    def save_benchmark_results(self, filename: str = None):
        """Save benchmark results to JSON file"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"vm_benchmark_results_{timestamp}.json"
        
        filepath = f"tests/data/{filename}"
        
        with open(filepath, 'w') as f:
            json.dump(self.benchmark_results, f, indent=2)
        
        logger.info(f"📊 Benchmark results saved to {filepath}")
        return filepath
    
    def print_summary(self):
        """Print benchmark summary"""
        logger.info("\n" + "="*60)
        logger.info("🎯 VM BENCHMARK SUMMARY")
        logger.info("="*60)
        
        # VM Startup Times
        if self.benchmark_results['vm_startup_times']:
            logger.info("\n🚀 VM Startup Times:")
            for result in self.benchmark_results['vm_startup_times']:
                logger.info(f"  {result['template']}: {result['avg_startup_time']:.2f}s avg")
        
        # VM Interactions
        if self.benchmark_results['vm_interaction_times']:
            logger.info("\n🔄 VM Interaction Times:")
            for result in self.benchmark_results['vm_interaction_times']:
                logger.info(f"  {result['interaction_type']}: {result['avg_time']:.3f}s avg")
        
        # Resource Usage
        if self.benchmark_results['vm_resource_usage']:
            logger.info("\n📊 VM Resource Usage:")
            for result in self.benchmark_results['vm_resource_usage']:
                logger.info(f"  {result['vm_count']} VMs: CPU +{result['cpu_increase']:.1f}%, "
                           f"Memory +{result['memory_increase']:.1f}%")
        
        # Cleanup Times
        if self.benchmark_results['vm_cleanup_times']:
            for result in self.benchmark_results['vm_cleanup_times']:
                logger.info(f"\n🗑️ VM Cleanup: {result['avg_cleanup_time']:.2f}s avg")

def run_vm_benchmarks():
    """Run complete VM benchmark suite"""
    logger.info("🎯 Starting VM Performance Benchmarks...")
    
    benchmarks = VMBenchmarkTests()
    
    try:
        # Run all benchmark tests
        benchmarks.benchmark_vm_startup_times(iterations=3)
        benchmarks.benchmark_vm_interactions(iterations=3)
        benchmarks.benchmark_vm_resource_usage()
        benchmarks.benchmark_vm_cleanup_times(iterations=3)
        
        # Save and display results
        results_file = benchmarks.save_benchmark_results()
        benchmarks.print_summary()
        
        logger.info(f"\n🎉 VM benchmarks completed! Results saved to {results_file}")
        
    except Exception as e:
        logger.error(f"❌ Benchmark failed: {e}")
        raise

if __name__ == "__main__":
    run_vm_benchmarks()
