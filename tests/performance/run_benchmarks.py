"""
TurdParty Performance Benchmark Suite
Runs comprehensive benchmarks for VM operations and MinIO storage
"""
import argparse
import logging
import time
import json
from datetime import datetime
from test_vm_benchmarks import run_vm_benchmarks, VMBenchmarkTests
from test_minio_benchmarks import run_minio_benchmarks, MinIOBenchmarkTests

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BenchmarkSuite:
    """Complete benchmark suite for TurdParty"""
    
    def __init__(self):
        self.results = {
            'suite_metadata': {
                'start_time': datetime.utcnow().isoformat(),
                'end_time': None,
                'total_duration': None
            },
            'vm_benchmarks': None,
            'minio_benchmarks': None,
            'summary': {}
        }
    
    def run_vm_benchmarks(self, iterations: int = 3):
        """Run VM performance benchmarks"""
        logger.info("🚀 Starting VM benchmarks...")
        
        vm_benchmarks = VMBenchmarkTests()
        
        try:
            vm_benchmarks.benchmark_vm_startup_times(iterations=iterations)
            vm_benchmarks.benchmark_vm_interactions(iterations=iterations)
            vm_benchmarks.benchmark_vm_resource_usage()
            vm_benchmarks.benchmark_vm_cleanup_times(iterations=iterations)
            
            self.results['vm_benchmarks'] = vm_benchmarks.benchmark_results
            logger.info("✅ VM benchmarks completed")
            
        except Exception as e:
            logger.error(f"❌ VM benchmarks failed: {e}")
            raise
    
    def run_minio_benchmarks(self, iterations: int = 3):
        """Run MinIO performance benchmarks"""
        logger.info("📦 Starting MinIO benchmarks...")
        
        minio_benchmarks = MinIOBenchmarkTests()
        
        try:
            minio_benchmarks.benchmark_file_upload_performance(iterations=iterations)
            minio_benchmarks.benchmark_file_download_performance()
            minio_benchmarks.benchmark_concurrent_operations(concurrent_uploads=iterations)
            minio_benchmarks.benchmark_storage_throughput()
            minio_benchmarks.benchmark_file_size_scaling()
            
            self.results['minio_benchmarks'] = minio_benchmarks.benchmark_results
            minio_benchmarks.cleanup_test_files()
            logger.info("✅ MinIO benchmarks completed")
            
        except Exception as e:
            logger.error(f"❌ MinIO benchmarks failed: {e}")
            raise
    
    def generate_summary(self):
        """Generate benchmark summary"""
        summary = {}
        
        # VM Summary
        if self.results['vm_benchmarks']:
            vm_data = self.results['vm_benchmarks']
            
            # Average startup times
            if vm_data['vm_startup_times']:
                avg_startup = sum(r['avg_startup_time'] for r in vm_data['vm_startup_times']) / len(vm_data['vm_startup_times'])
                summary['avg_vm_startup_time'] = avg_startup
            
            # Average interaction times
            if vm_data['vm_interaction_times']:
                avg_interaction = sum(r['avg_time'] for r in vm_data['vm_interaction_times']) / len(vm_data['vm_interaction_times'])
                summary['avg_vm_interaction_time'] = avg_interaction
            
            # Resource usage
            if vm_data['vm_resource_usage']:
                max_cpu_increase = max(r['cpu_increase'] for r in vm_data['vm_resource_usage'])
                max_memory_increase = max(r['memory_increase'] for r in vm_data['vm_resource_usage'])
                summary['max_cpu_increase'] = max_cpu_increase
                summary['max_memory_increase'] = max_memory_increase
        
        # MinIO Summary
        if self.results['minio_benchmarks']:
            minio_data = self.results['minio_benchmarks']
            
            # Average upload throughput
            if minio_data['file_upload_performance']:
                avg_throughput = sum(r['avg_throughput_mbps'] for r in minio_data['file_upload_performance']) / len(minio_data['file_upload_performance'])
                summary['avg_upload_throughput_mbps'] = avg_throughput
            
            # Concurrent performance
            if minio_data['concurrent_operations']:
                max_concurrent_rate = max(r['files_per_second'] for r in minio_data['concurrent_operations'])
                summary['max_concurrent_files_per_second'] = max_concurrent_rate
            
            # Storage throughput
            if minio_data['storage_throughput']:
                max_storage_throughput = max(r['throughput_mbps'] for r in minio_data['storage_throughput'])
                summary['max_storage_throughput_mbps'] = max_storage_throughput
        
        self.results['summary'] = summary
    
    def save_results(self, filename: str = None):
        """Save complete benchmark results"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"complete_benchmark_results_{timestamp}.json"
        
        # Ensure data directory exists
        import os
        data_dir = "../data"
        os.makedirs(data_dir, exist_ok=True)
        filepath = f"{data_dir}/{filename}"
        
        with open(filepath, 'w') as f:
            json.dump(self.results, f, indent=2)
        
        logger.info(f"📊 Complete benchmark results saved to {filepath}")
        return filepath
    
    def print_complete_summary(self):
        """Print complete benchmark summary"""
        logger.info("\n" + "="*80)
        logger.info("🎯 TURDPARTY COMPLETE BENCHMARK SUMMARY")
        logger.info("="*80)
        
        if self.results['summary']:
            summary = self.results['summary']
            
            logger.info("\n🚀 VM PERFORMANCE:")
            if 'avg_vm_startup_time' in summary:
                logger.info(f"  Average VM Startup Time: {summary['avg_vm_startup_time']:.2f}s")
            if 'avg_vm_interaction_time' in summary:
                logger.info(f"  Average VM Interaction Time: {summary['avg_vm_interaction_time']:.3f}s")
            if 'max_cpu_increase' in summary:
                logger.info(f"  Max CPU Increase: {summary['max_cpu_increase']:.1f}%")
            if 'max_memory_increase' in summary:
                logger.info(f"  Max Memory Increase: {summary['max_memory_increase']:.1f}%")
            
            logger.info("\n📦 MINIO PERFORMANCE:")
            if 'avg_upload_throughput_mbps' in summary:
                logger.info(f"  Average Upload Throughput: {summary['avg_upload_throughput_mbps']:.2f} MB/s")
            if 'max_concurrent_files_per_second' in summary:
                logger.info(f"  Max Concurrent Rate: {summary['max_concurrent_files_per_second']:.2f} files/s")
            if 'max_storage_throughput_mbps' in summary:
                logger.info(f"  Max Storage Throughput: {summary['max_storage_throughput_mbps']:.2f} MB/s")
        
        # Duration
        if self.results['suite_metadata']['total_duration']:
            logger.info(f"\n⏱️ Total Benchmark Duration: {self.results['suite_metadata']['total_duration']:.2f}s")
        
        logger.info("\n🎉 BENCHMARK SUITE COMPLETED SUCCESSFULLY!")

def main():
    """Main benchmark runner"""
    parser = argparse.ArgumentParser(description='TurdParty Performance Benchmark Suite')
    parser.add_argument('--vm-only', action='store_true', help='Run only VM benchmarks')
    parser.add_argument('--minio-only', action='store_true', help='Run only MinIO benchmarks')
    parser.add_argument('--iterations', type=int, default=3, help='Number of iterations for tests')
    parser.add_argument('--output', type=str, help='Output filename for results')
    parser.add_argument('--quick', action='store_true', help='Run quick benchmarks (fewer iterations)')
    
    args = parser.parse_args()
    
    # Adjust iterations for quick mode
    if args.quick:
        iterations = 2
        logger.info("🏃 Running in quick mode (fewer iterations)")
    else:
        iterations = args.iterations
    
    logger.info("🎯 Starting TurdParty Performance Benchmark Suite")
    logger.info(f"Iterations: {iterations}")
    
    suite = BenchmarkSuite()
    start_time = time.time()
    
    try:
        # Run benchmarks based on arguments
        if args.minio_only:
            suite.run_minio_benchmarks(iterations=iterations)
        elif args.vm_only:
            suite.run_vm_benchmarks(iterations=iterations)
        else:
            # Run both
            suite.run_vm_benchmarks(iterations=iterations)
            suite.run_minio_benchmarks(iterations=iterations)
        
        # Finalize results
        end_time = time.time()
        suite.results['suite_metadata']['end_time'] = datetime.utcnow().isoformat()
        suite.results['suite_metadata']['total_duration'] = end_time - start_time
        
        # Generate summary
        suite.generate_summary()
        
        # Save and display results
        results_file = suite.save_results(args.output)
        suite.print_complete_summary()
        
        logger.info(f"\n📊 Results saved to: {results_file}")
        
    except KeyboardInterrupt:
        logger.info("\n⚠️ Benchmark interrupted by user")
    except Exception as e:
        logger.error(f"\n❌ Benchmark suite failed: {e}")
        raise

if __name__ == "__main__":
    main()
