"""
Unit tests for VM Metrics Service
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime, timezone

from api.services.vm_metrics_service import VMMetricsService, vm_metrics_service


class TestVMMetricsService:
    """Test VM Metrics Service functionality"""

    @pytest.fixture
    def metrics_service(self):
        """Create a fresh VM metrics service instance"""
        service = VMMetricsService()
        return service

    @pytest.fixture
    def mock_docker_client(self):
        """Mock Docker client"""
        mock_client = Mock()
        mock_container = Mock()
        mock_container.id = "test_container_123"
        mock_container.status = "running"
        mock_container.attrs = {
            'State': {
                'StartedAt': '2023-11-04T10:30:00.000000000Z'
            }
        }
        
        # Mock stats
        mock_stats = {
            'cpu_stats': {
                'cpu_usage': {'total_usage': 1000000000},
                'system_cpu_usage': 2000000000
            },
            'precpu_stats': {
                'cpu_usage': {'total_usage': 500000000},
                'system_cpu_usage': 1000000000
            },
            'memory_stats': {
                'usage': 1073741824,  # 1GB
                'limit': 2147483648   # 2GB
            },
            'networks': {
                'eth0': {
                    'rx_bytes': 1048576,
                    'tx_bytes': 524288
                }
            }
        }
        
        mock_container.stats.return_value = mock_stats
        mock_container.exec_run.return_value = Mock(
            exit_code=0,
            output=(b"root      1234  0.1  0.5  12345  6789 ?        S    10:30   0:01 python\n", None)
        )
        
        mock_client.containers.get.return_value = mock_container
        return mock_client

    @pytest.mark.asyncio
    async def test_initialize_success(self, metrics_service):
        """Test successful initialization"""
        with patch('docker.from_env') as mock_docker:
            mock_docker.return_value = Mock()
            
            await metrics_service.initialize()
            
            assert metrics_service.docker_client is not None
            mock_docker.assert_called_once()

    @pytest.mark.asyncio
    async def test_initialize_docker_failure(self, metrics_service):
        """Test initialization with Docker failure"""
        with patch('docker.from_env') as mock_docker:
            mock_docker.side_effect = Exception("Docker not available")
            
            await metrics_service.initialize()
            
            assert metrics_service.docker_client is None

    @pytest.mark.asyncio
    async def test_get_docker_metrics_success(self, metrics_service, mock_docker_client):
        """Test successful Docker metrics collection"""
        metrics_service.docker_client = mock_docker_client
        
        result = await metrics_service._get_docker_metrics("test_container")
        
        assert result['vm_id'] == "test_container"
        assert result['vm_type'] == "docker"
        assert result['status'] == "running"
        assert 'cpu_percent' in result
        assert 'memory_percent' in result
        assert 'network_rx_bytes' in result
        assert 'network_tx_bytes' in result
        assert 'top_processes' in result

    @pytest.mark.asyncio
    async def test_get_docker_metrics_container_not_found(self, metrics_service, mock_docker_client):
        """Test Docker metrics with container not found"""
        mock_docker_client.containers.get.side_effect = Exception("Container not found")
        metrics_service.docker_client = mock_docker_client
        
        result = await metrics_service._get_docker_metrics("nonexistent")
        
        assert result['status'] == "error"
        assert 'error' in result
        assert "Container not found" in result['error']

    @pytest.mark.asyncio
    async def test_get_vagrant_metrics(self, metrics_service):
        """Test Vagrant metrics collection (mock)"""
        result = await metrics_service._get_vagrant_metrics("test_vm")
        
        assert result['vm_id'] == "test_vm"
        assert result['vm_type'] == "vagrant"
        assert result['status'] == "running"
        assert result['cpu_percent'] == 25.5
        assert result['memory_percent'] == 45.2

    @pytest.mark.asyncio
    async def test_get_vm_metrics_docker(self, metrics_service, mock_docker_client):
        """Test get_vm_metrics for Docker"""
        metrics_service.docker_client = mock_docker_client
        
        result = await metrics_service.get_vm_metrics("test_vm", "docker")
        
        assert result['vm_type'] == "docker"
        assert 'cpu_percent' in result

    @pytest.mark.asyncio
    async def test_get_vm_metrics_vagrant(self, metrics_service):
        """Test get_vm_metrics for Vagrant"""
        result = await metrics_service.get_vm_metrics("test_vm", "vagrant")
        
        assert result['vm_type'] == "vagrant"
        assert result['cpu_percent'] == 25.5

    @pytest.mark.asyncio
    async def test_get_vm_metrics_invalid_type(self, metrics_service):
        """Test get_vm_metrics with invalid VM type"""
        result = await metrics_service.get_vm_metrics("test_vm", "invalid")
        
        assert result['status'] == "error"
        assert "Unsupported VM type" in result['error']

    def test_calculate_cpu_percent_valid_stats(self, metrics_service):
        """Test CPU percentage calculation with valid stats"""
        stats = {
            'cpu_stats': {
                'cpu_usage': {
                    'total_usage': 2000000000,
                    'percpu_usage': [500000000, 500000000]
                },
                'system_cpu_usage': 4000000000
            },
            'precpu_stats': {
                'cpu_usage': {
                    'total_usage': 1000000000
                },
                'system_cpu_usage': 2000000000
            }
        }
        
        result = metrics_service._calculate_cpu_percent(stats)
        
        assert isinstance(result, float)
        assert result >= 0
        assert result <= 200  # Max for 2 CPUs

    def test_calculate_cpu_percent_zero_delta(self, metrics_service):
        """Test CPU percentage calculation with zero delta"""
        stats = {
            'cpu_stats': {
                'cpu_usage': {'total_usage': 1000000000},
                'system_cpu_usage': 2000000000
            },
            'precpu_stats': {
                'cpu_usage': {'total_usage': 1000000000},
                'system_cpu_usage': 2000000000
            }
        }
        
        result = metrics_service._calculate_cpu_percent(stats)
        
        assert result == 0.0

    def test_calculate_cpu_percent_invalid_stats(self, metrics_service):
        """Test CPU percentage calculation with invalid stats"""
        stats = {}
        
        result = metrics_service._calculate_cpu_percent(stats)
        
        assert result == 0.0

    @pytest.mark.asyncio
    async def test_stream_vm_metrics(self, metrics_service, mock_docker_client):
        """Test VM metrics streaming"""
        metrics_service.docker_client = mock_docker_client
        
        # Collect a few metrics from the stream
        metrics_collected = []
        async for metrics in metrics_service.stream_vm_metrics("test_vm", "docker", interval=0.1):
            metrics_collected.append(metrics)
            if len(metrics_collected) >= 3:
                break
        
        assert len(metrics_collected) == 3
        for metrics in metrics_collected:
            assert metrics['vm_id'] == "test_vm"
            assert 'timestamp' in metrics

    @pytest.mark.asyncio
    async def test_stream_vm_metrics_stop(self, metrics_service):
        """Test stopping VM metrics stream"""
        # Start a stream
        stream_task = asyncio.create_task(
            metrics_service.stream_vm_metrics("test_vm", "docker", interval=0.1).__anext__()
        )
        
        # Stop the stream
        metrics_service.stop_stream("test_vm", "docker")

        # Wait for stream to stop and verify cleanup
        await asyncio.sleep(0.5)

        # Check if stream is properly stopped (either cancelled or completed)
        stream_key = "test_vm:docker"
        is_stream_stopped = (
            stream_task.done() or
            stream_task.cancelled() or
            stream_key not in metrics_service.active_streams or
            not metrics_service.active_streams.get(stream_key, True)
        )
        assert is_stream_stopped, "Stream should be stopped after stop_stream() call"

    def test_get_error_metrics(self, metrics_service):
        """Test error metrics generation"""
        error_msg = "Test error message"
        
        result = metrics_service._get_error_metrics(error_msg)
        
        assert result['status'] == "error"
        assert result['error'] == error_msg
        assert result['cpu_percent'] == 0.0
        assert result['memory_percent'] == 0.0

    @pytest.mark.asyncio
    async def test_docker_processes_parsing(self, metrics_service):
        """Test Docker process list parsing"""
        mock_container = Mock()
        mock_container.exec_run.return_value = Mock(
            exit_code=0,
            output=(
                b"root      1234  15.2  25.6  123456  789012 ?        R    10:30   0:01 python app.py\n"
                b"www-data  5678   2.1   5.3   67890   12345 ?        S    10:29   0:00 nginx\n",
                None
            )
        )
        
        result = await metrics_service._get_docker_processes(mock_container)
        
        assert len(result) == 2
        assert result[0]['pid'] == 1234
        assert result[0]['name'] == "python"
        assert result[0]['cpu_percent'] == 15.2
        assert result[1]['pid'] == 5678
        assert result[1]['name'] == "nginx"

    @pytest.mark.asyncio
    async def test_docker_processes_exec_failure(self, metrics_service):
        """Test Docker process list with exec failure"""
        mock_container = Mock()
        mock_container.exec_run.return_value = Mock(
            exit_code=1,
            output=(None, b"Permission denied")
        )
        
        result = await metrics_service._get_docker_processes(mock_container)
        
        assert result == []

    def test_get_container_uptime_valid(self, metrics_service):
        """Test container uptime calculation"""
        mock_container = Mock()
        start_time = datetime.now(timezone.utc).isoformat().replace('+00:00', 'Z')
        mock_container.attrs = {
            'State': {
                'StartedAt': start_time
            }
        }
        
        result = metrics_service._get_container_uptime(mock_container)
        
        assert isinstance(result, int)
        assert result >= 0

    def test_get_container_uptime_invalid(self, metrics_service):
        """Test container uptime with invalid data"""
        mock_container = Mock()
        mock_container.attrs = {}
        
        result = metrics_service._get_container_uptime(mock_container)
        
        assert result == 0


class TestVMMetricsServiceIntegration:
    """Integration tests for VM Metrics Service"""

    @pytest.mark.asyncio
    async def test_global_service_instance(self):
        """Test the global service instance"""
        # Test that the global instance works
        await vm_metrics_service.initialize()
        
        # Test getting metrics (will fail gracefully without Docker)
        metrics = await vm_metrics_service.get_vm_metrics("test", "docker")
        
        assert 'vm_id' in metrics
        assert 'timestamp' in metrics

    @pytest.mark.asyncio
    async def test_concurrent_metrics_collection(self):
        """Test concurrent metrics collection"""
        tasks = []
        for i in range(5):
            task = asyncio.create_task(
                vm_metrics_service.get_vm_metrics(f"test_vm_{i}", "docker")
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        assert len(results) == 5
        for result in results:
            if not isinstance(result, Exception):
                assert 'vm_id' in result

    @pytest.mark.asyncio
    async def test_metrics_service_error_handling(self):
        """Test error handling in metrics service"""
        # Test with invalid VM type
        result = await vm_metrics_service.get_vm_metrics("test", "invalid_type")
        
        assert result['status'] == "error"
        assert 'error' in result

    @pytest.mark.asyncio
    async def test_stream_cleanup(self):
        """Test stream cleanup functionality"""
        vm_id = "test_cleanup_vm"
        vm_type = "docker"
        
        # Start a stream
        stream_gen = vm_metrics_service.stream_vm_metrics(vm_id, vm_type, interval=0.1)
        
        # Get one metrics point
        first_metrics = await stream_gen.__anext__()
        assert first_metrics is not None
        
        # Stop the stream
        vm_metrics_service.stop_stream(vm_id, vm_type)
        
        # Verify stream key is cleaned up
        stream_key = f"{vm_id}:{vm_type}"
        assert stream_key not in vm_metrics_service.active_streams or \
               not vm_metrics_service.active_streams[stream_key]
