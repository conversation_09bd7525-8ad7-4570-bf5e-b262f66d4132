"""
Enhanced VM Management Unit Tests
Based on reference repository patterns and comprehensive testing
"""

import pytest
import uuid
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime, timedelta

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from services.api.src.routes.v1.vms import (
    VMTemplate, VMAction, VMCreateRequest, VMActionRequest, VMResponse
)
from services.api.src.models.vm_instance import VMInstance, VMStatus


class TestVMTemplates:
    """Test VM template functionality."""
    
    def test_vm_template_enum_values(self):
        """Test that VM template enum has expected values."""
        expected_templates = [
            "ubuntu/focal64", "ubuntu/jammy64", "ubuntu/bionic64",
            "debian/bullseye64", "centos/7", "centos/8", "alpine/alpine64",
            "ubuntu:20.04", "ubuntu:22.04", "alpine:latest", "custom"
        ]
        
        actual_templates = [template.value for template in VMTemplate]
        
        for expected in expected_templates:
            assert expected in actual_templates
    
    def test_vm_template_compatibility(self):
        """Test VM template compatibility with different VM types."""
        # Docker-specific templates
        docker_templates = [
            VMTemplate.DOCKER_UBUNTU_2004,
            VMTemplate.DOCKER_UBUNTU_2204,
            VMTemplate.DOCKER_ALPINE
        ]
        
        for template in docker_templates:
            assert template.value.startswith(("ubuntu:", "alpine:"))
        
        # Vagrant-compatible templates
        vagrant_templates = [
            VMTemplate.UBUNTU_2004,
            VMTemplate.UBUNTU_2204,
            VMTemplate.DEBIAN_11
        ]
        
        for template in vagrant_templates:
            assert "/" in template.value or template == VMTemplate.CUSTOM


class TestVMCreateRequest:
    """Test VM creation request validation."""
    
    def test_valid_vm_create_request(self):
        """Test valid VM creation request."""
        request = VMCreateRequest(
            name="test-vm",
            template=VMTemplate.UBUNTU_2004,
            vm_type="docker",
            memory_mb=1024,
            cpus=2,
            disk_gb=20,
            domain="TurdParty",
            description="Test VM",
            auto_start=True
        )
        
        assert request.name == "test-vm"
        assert request.template == VMTemplate.UBUNTU_2004
        assert request.vm_type == "docker"
        assert request.memory_mb == 1024
        assert request.cpus == 2
        assert request.disk_gb == 20
        assert request.domain == "TurdParty"
        assert request.auto_start is True
    
    def test_vm_create_request_defaults(self):
        """Test VM creation request with default values."""
        request = VMCreateRequest(name="test-vm")
        
        assert request.template == VMTemplate.UBUNTU_2004
        assert request.vm_type == "docker"
        assert request.memory_mb == 1024
        assert request.cpus == 1
        assert request.disk_gb == 20
        assert request.domain == "TurdParty"
        assert request.auto_start is True
    
    def test_vm_create_request_validation(self):
        """Test VM creation request validation."""
        # Test memory limits
        with pytest.raises(ValueError):
            VMCreateRequest(name="test-vm", memory_mb=100)  # Below minimum
        
        with pytest.raises(ValueError):
            VMCreateRequest(name="test-vm", memory_mb=10000)  # Above maximum
        
        # Test CPU limits
        with pytest.raises(ValueError):
            VMCreateRequest(name="test-vm", cpus=0)  # Below minimum
        
        with pytest.raises(ValueError):
            VMCreateRequest(name="test-vm", cpus=16)  # Above maximum
        
        # Test disk limits
        with pytest.raises(ValueError):
            VMCreateRequest(name="test-vm", disk_gb=1)  # Below minimum
        
        with pytest.raises(ValueError):
            VMCreateRequest(name="test-vm", disk_gb=500)  # Above maximum


class TestVMActionRequest:
    """Test VM action request validation."""
    
    def test_valid_vm_actions(self):
        """Test valid VM actions."""
        valid_actions = [
            VMAction.START, VMAction.STOP, VMAction.RESTART,
            VMAction.DESTROY, VMAction.SUSPEND, VMAction.RESUME
        ]
        
        for action in valid_actions:
            request = VMActionRequest(action=action, force=False)
            assert request.action == action
            assert request.force is False
    
    def test_vm_action_with_force(self):
        """Test VM action with force flag."""
        request = VMActionRequest(action=VMAction.STOP, force=True)
        assert request.action == VMAction.STOP
        assert request.force is True


class TestVMResponse:
    """Test VM response model."""
    
    def test_vm_response_creation(self):
        """Test VM response model creation."""
        response = VMResponse(
            vm_id="123e4567-e89b-12d3-a456-426614174000",
            name="test-vm",
            template="ubuntu:20.04",
            vm_type="docker",
            memory_mb=1024,
            cpus=2,
            disk_gb=20,
            status="running",
            domain="TurdParty",
            runtime_minutes=15.5,
            is_expired=False,
            created_at="2025-06-10T10:00:00Z"
        )
        
        assert response.vm_id == "123e4567-e89b-12d3-a456-426614174000"
        assert response.name == "test-vm"
        assert response.template == "ubuntu:20.04"
        assert response.vm_type == "docker"
        assert response.memory_mb == 1024
        assert response.cpus == 2
        assert response.disk_gb == 20
        assert response.status == "running"
        assert response.domain == "TurdParty"
        assert response.runtime_minutes == 15.5
        assert response.is_expired is False


class TestVMInstance:
    """Test VM instance model functionality."""
    
    def test_vm_instance_creation(self):
        """Test VM instance model creation."""
        vm = VMInstance(
            name="test-vm",
            template="ubuntu:20.04",
            memory_mb=1024,
            cpus=2,
            disk_gb=20,
            status=VMStatus.CREATING
        )
        
        assert vm.name == "test-vm"
        assert vm.template == "ubuntu:20.04"
        assert vm.memory_mb == 1024
        assert vm.cpus == 2
        assert vm.disk_gb == 20
        assert vm.status == VMStatus.CREATING
    
    def test_vm_instance_runtime_calculation(self):
        """Test VM instance runtime calculation."""
        vm = VMInstance(
            name="test-vm",
            template="ubuntu:20.04",
            status=VMStatus.RUNNING
        )
        
        # Set started time to 10 minutes ago
        vm.started_at = datetime.utcnow() - timedelta(minutes=10)
        
        # Runtime should be approximately 10 minutes
        runtime = vm.runtime_minutes
        assert 9.5 <= runtime <= 10.5
    
    def test_vm_instance_expiration_check(self):
        """Test VM instance expiration check."""
        vm = VMInstance(
            name="test-vm",
            template="ubuntu:20.04",
            status=VMStatus.RUNNING
        )
        
        # VM started 35 minutes ago (expired)
        vm.started_at = datetime.utcnow() - timedelta(minutes=35)
        assert vm.is_expired is True
        
        # VM started 25 minutes ago (not expired)
        vm.started_at = datetime.utcnow() - timedelta(minutes=25)
        assert vm.is_expired is False
        
        # VM not started yet
        vm.started_at = None
        assert vm.is_expired is False


class TestVMManagementIntegration:
    """Test VM management integration scenarios."""
    
    @pytest.fixture
    def mock_db_session(self):
        """Mock database session."""
        session = Mock(spec=AsyncSession)
        session.execute = AsyncMock()
        session.add = Mock()
        session.commit = AsyncMock()
        session.refresh = AsyncMock()
        session.rollback = AsyncMock()
        return session
    
    @pytest.fixture
    def mock_celery_app(self):
        """Mock Celery application."""
        celery_app = Mock()
        task = Mock()
        task.id = "test-task-id"
        celery_app.send_task.return_value = task
        return celery_app
    
    def test_domain_enforcement(self):
        """Test that non-TurdParty domains are rejected."""
        request = VMCreateRequest(
            name="test-vm",
            domain="InvalidDomain"
        )
        
        # This should be handled in the API endpoint
        assert request.domain == "InvalidDomain"
        # The actual enforcement happens in the API layer
    
    def test_template_conversion_for_docker(self):
        """Test template conversion for Docker VMs."""
        # Test conversion mapping
        conversions = {
            VMTemplate.UBUNTU_2004: "ubuntu:20.04",
            VMTemplate.UBUNTU_2204: "ubuntu:22.04",
            VMTemplate.UBUNTU_1804: "ubuntu:18.04",
            VMTemplate.DEBIAN_11: "debian:bullseye",
            VMTemplate.CENTOS_7: "centos:7",
            VMTemplate.CENTOS_8: "centos:8",
            VMTemplate.ALPINE: "alpine:latest"
        }
        
        for vagrant_template, expected_docker in conversions.items():
            # This logic would be in the API endpoint
            if vagrant_template.value.startswith(("ubuntu:", "alpine:", "centos:", "debian:")):
                docker_template = vagrant_template.value
            else:
                docker_template = expected_docker
            
            assert docker_template == expected_docker
    
    @patch('services.api.src.routes.v1.vms.get_celery_app')
    async def test_vm_creation_workflow(self, mock_get_celery, mock_db_session, mock_celery_app):
        """Test complete VM creation workflow."""
        mock_get_celery.return_value = mock_celery_app
        
        # Mock database query result (no existing VM)
        mock_result = Mock()
        mock_result.scalar_one_or_none.return_value = None
        mock_db_session.execute.return_value = mock_result
        
        # Create VM request
        vm_request = VMCreateRequest(
            name="test-vm",
            template=VMTemplate.DOCKER_UBUNTU_2004,
            vm_type="docker",
            memory_mb=512,
            cpus=1,
            domain="TurdParty"
        )
        
        # Verify request structure
        assert vm_request.name == "test-vm"
        assert vm_request.template == VMTemplate.DOCKER_UBUNTU_2004
        assert vm_request.vm_type == "docker"
        assert vm_request.domain == "TurdParty"
    
    def test_vm_action_task_mapping(self):
        """Test VM action to task mapping."""
        task_mapping = {
            VMAction.START: "services.workers.tasks.vm_management.start_vm",
            VMAction.STOP: "services.workers.tasks.vm_management.stop_vm",
            VMAction.RESTART: "services.workers.tasks.vm_management.restart_vm",
            VMAction.DESTROY: "services.workers.tasks.vm_management.delete_vm",
            VMAction.SUSPEND: "services.workers.tasks.vm_management.suspend_vm",
            VMAction.RESUME: "services.workers.tasks.vm_management.resume_vm"
        }
        
        for action, expected_task in task_mapping.items():
            assert expected_task.endswith(f"{action.value}_vm") or action == VMAction.DESTROY
    
    def test_vm_status_transitions(self):
        """Test valid VM status transitions."""
        valid_transitions = {
            VMStatus.CREATING: [VMStatus.RUNNING, VMStatus.FAILED],
            VMStatus.RUNNING: [VMStatus.TERMINATING, VMStatus.FAILED],
            VMStatus.TERMINATING: [VMStatus.TERMINATED, VMStatus.FAILED],
            VMStatus.TERMINATED: [],  # Terminal state
            VMStatus.FAILED: [VMStatus.CREATING]  # Can retry
        }
        
        for from_status, to_statuses in valid_transitions.items():
            for to_status in to_statuses:
                # This would be validated in the business logic
                assert from_status != to_status or from_status == VMStatus.FAILED


class TestVMManagementErrorHandling:
    """Test error handling in VM management."""
    
    def test_vm_not_found_scenarios(self):
        """Test VM not found error scenarios."""
        fake_vm_id = uuid.uuid4()
        
        # These would be tested in the API integration tests
        # Here we just verify the UUID format
        assert isinstance(fake_vm_id, uuid.UUID)
        assert len(str(fake_vm_id)) == 36
    
    def test_invalid_vm_actions(self):
        """Test invalid VM action handling."""
        # Test that invalid actions are properly rejected
        valid_actions = {action.value for action in VMAction}
        
        invalid_actions = ["invalid_action", "unknown", ""]
        
        for invalid_action in invalid_actions:
            assert invalid_action not in valid_actions
    
    def test_resource_limit_validation(self):
        """Test resource limit validation."""
        # Memory limits
        assert 256 >= 256  # Minimum memory
        assert 8192 <= 8192  # Maximum memory
        
        # CPU limits
        assert 1 >= 1  # Minimum CPUs
        assert 8 <= 8  # Maximum CPUs
        
        # Disk limits
        assert 5 >= 5  # Minimum disk
        assert 100 <= 100  # Maximum disk


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
