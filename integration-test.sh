#!/bin/bash

# Comprehensive integration test for TurdParty system
# Tests frontend, API, and full workflow integration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_colored() {
    echo -e "${1}${2}${NC}"
}

print_colored $PURPLE "🧪 TurdParty System Integration Test"
echo ""

PASSED=0
TOTAL=0

# Test 1: Core Services Health
print_colored $YELLOW "📋 Test 1: Core Services Health Check"
TOTAL=$((TOTAL + 1))

SERVICES=("api" "frontend" "database" "cache" "storage")
SERVICE_HEALTH=0

for service in "${SERVICES[@]}"; do
    if docker ps --filter "name=turdpartycollab_$service" --format "{{.Status}}" | grep -q "healthy\|Up"; then
        print_colored $CYAN "   ✅ $service: healthy"
        SERVICE_HEALTH=$((SERVICE_HEALTH + 1))
    else
        print_colored $CYAN "   ❌ $service: unhealthy"
    fi
done

if [ $SERVICE_HEALTH -eq ${#SERVICES[@]} ]; then
    print_colored $GREEN "✅ Core services health: PASS ($SERVICE_HEALTH/${#SERVICES[@]})"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Core services health: FAIL ($SERVICE_HEALTH/${#SERVICES[@]})"
fi

# Test 2: Frontend Accessibility
print_colored $YELLOW "📋 Test 2: Frontend Accessibility"
TOTAL=$((TOTAL + 1))

if curl -f -s http://localhost:3000/health | grep -q "healthy"; then
    print_colored $GREEN "✅ Frontend accessibility: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Frontend accessibility: FAIL"
fi

# Test 3: API Connectivity
print_colored $YELLOW "📋 Test 3: API Connectivity"
TOTAL=$((TOTAL + 1))

if curl -f -s http://localhost:8000/health | grep -q "healthy"; then
    print_colored $GREEN "✅ API connectivity: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ API connectivity: FAIL"
fi

# Test 4: Database Connection
print_colored $YELLOW "📋 Test 4: Database Connection"
TOTAL=$((TOTAL + 1))

if curl -f -s http://localhost:8000/api/v1/health | grep -q "healthy\|ok"; then
    print_colored $GREEN "✅ Database connection: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Database connection: FAIL"
fi

# Test 5: Storage Service
print_colored $YELLOW "📋 Test 5: Storage Service (MinIO)"
TOTAL=$((TOTAL + 1))

if curl -f -s http://localhost:9000/minio/health/live > /dev/null; then
    print_colored $GREEN "✅ Storage service: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Storage service: FAIL"
fi

# Test 6: Cache Service
print_colored $YELLOW "📋 Test 6: Cache Service (Redis)"
TOTAL=$((TOTAL + 1))

if docker exec turdpartycollab_cache redis-cli ping | grep -q "PONG"; then
    print_colored $GREEN "✅ Cache service: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Cache service: FAIL"
fi

# Test 7: Worker Services
print_colored $YELLOW "📋 Test 7: Worker Services"
TOTAL=$((TOTAL + 1))

WORKER_COUNT=$(docker ps --filter "name=turdpartycollab_" --filter "name=worker" --format "{{.Names}}" | wc -l)
if [ $WORKER_COUNT -gt 0 ]; then
    print_colored $GREEN "✅ Worker services: PASS ($WORKER_COUNT workers running)"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Worker services: FAIL (no workers found)"
fi

# Test 8: VM Management API
print_colored $YELLOW "📋 Test 8: VM Management API"
TOTAL=$((TOTAL + 1))

if curl -f -s http://localhost:8000/api/v1/vms/templates > /dev/null; then
    print_colored $GREEN "✅ VM management API: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ VM management API: FAIL"
fi

# Test 9: File Management API
print_colored $YELLOW "📋 Test 9: File Management API"
TOTAL=$((TOTAL + 1))

if curl -f -s http://localhost:8000/api/v1/files > /dev/null; then
    print_colored $GREEN "✅ File management API: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ File management API: FAIL"
fi

# Test 10: Frontend-API Integration
print_colored $YELLOW "📋 Test 10: Frontend-API Integration"
TOTAL=$((TOTAL + 1))

# Test if frontend can proxy to API
if curl -s http://localhost:3000/api/v1/health | grep -q "healthy\|ok\|404"; then
    print_colored $GREEN "✅ Frontend-API integration: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Frontend-API integration: FAIL"
fi

# Test 11: Traefik Integration
print_colored $YELLOW "📋 Test 11: Traefik Integration"
TOTAL=$((TOTAL + 1))

TRAEFIK_SERVICES=$(curl -s http://localhost:8080/api/http/services | grep -c "turdparty")
if [ $TRAEFIK_SERVICES -gt 0 ]; then
    print_colored $GREEN "✅ Traefik integration: PASS ($TRAEFIK_SERVICES services registered)"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Traefik integration: FAIL"
fi

# Test 12: Network Connectivity
print_colored $YELLOW "📋 Test 12: Internal Network Connectivity"
TOTAL=$((TOTAL + 1))

if docker exec turdpartycollab_frontend curl -f -s http://turdpartycollab_api:8000/health > /dev/null; then
    print_colored $GREEN "✅ Internal network connectivity: PASS"
    PASSED=$((PASSED + 1))
else
    print_colored $RED "❌ Internal network connectivity: FAIL"
fi

# Calculate success rate
SUCCESS_RATE=$(( (PASSED * 100) / TOTAL ))

echo ""
print_colored $PURPLE "📊 Integration Test Results:"
print_colored $BLUE "   Total Tests: $TOTAL"
print_colored $GREEN "   Passed: $PASSED"
print_colored $RED "   Failed: $((TOTAL - PASSED))"
print_colored $BLUE "   Success Rate: $SUCCESS_RATE%"

echo ""
if [ $SUCCESS_RATE -ge 90 ]; then
    print_colored $GREEN "🎉 EXCELLENT: System integration is working perfectly!"
    print_colored $GREEN "✅ TurdParty system is ready for production use"
elif [ $SUCCESS_RATE -ge 80 ]; then
    print_colored $YELLOW "👍 GOOD: System integration is working well"
    print_colored $YELLOW "⚠️  Minor issues detected but system is functional"
elif [ $SUCCESS_RATE -ge 70 ]; then
    print_colored $YELLOW "⚠️  FAIR: System has some integration issues"
    print_colored $YELLOW "🔧 Some components need attention"
else
    print_colored $RED "❌ POOR: Significant integration issues detected"
    print_colored $RED "🚨 System requires immediate attention"
fi

echo ""
print_colored $BLUE "🌐 System Access Points:"
print_colored $CYAN "   Frontend: http://localhost:3000"
print_colored $CYAN "   API: http://localhost:8000"
print_colored $CYAN "   Storage: http://localhost:9000"
print_colored $CYAN "   Status: http://localhost:8090"
print_colored $CYAN "   Traefik: http://localhost:8080"

echo ""
print_colored $BLUE "🎯 Workflow Ready:"
print_colored $CYAN "   📁 File Upload: http://localhost:3000/file_upload"
print_colored $CYAN "   📋 File Selection: http://localhost:3000/file_selection"
print_colored $CYAN "   🖥️ VM Injection: http://localhost:3000/vm_injection"
print_colored $CYAN "   📊 VM Status: http://localhost:3000/vm_status"

echo ""
