version: '3.8'

services:
  api:
    container_name: turdpartycollab_api
    build:
      context: .
      dockerfile: docker/Dockerfile
    # Host networking mode - API accessible on host port 8000
    # gRPC port 40000 accessible directly from host
    environment:
      - PYTHONPATH=/app
      - DEBUG=true
      - TEST_MODE=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - LOGSTASH_HOST=logstash
      - LOGSTASH_PORT=5000
      # WebSocket Configuration
      - WEBSOCKET_HEARTBEAT_INTERVAL=30
      - METRICS_STREAMING_INTERVAL=1
      - MAX_CONCURRENT_STREAMS=100
      # File Transfer Settings
      - FILE_CHUNK_SIZE=65536
      - MAX_FILE_SIZE_MB=1024
      - UPLOAD_PROGRESS_UPDATE_INTERVAL=0.5
      # VM Communication
      - VM_COMMUNICATION_PROTOCOL=auto
      - SSH_FALLBACK_ENABLED=true
      - GRPC_RETRY_ATTEMPTS=3
      - VAGRANT_GRPC_PORT=40000
      - ENABLE_GRPC_COMMUNICATION=true
      # Testing
      - TESTING=false
      - LOG_LEVEL=INFO
    volumes:
      - ./api:/app/api
      - ./scripts:/app/scripts
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - /var/run/docker.sock:/var/run/docker.sock  # For Docker VM management
      - ./tests:/app/tests  # Test files for development
    depends_on:
      - elasticsearch
      - logstash
    # Use host networking for direct access to Vagrant gRPC on port 40000
    network_mode: "host"
    restart: unless-stopped
    # Note: Host networking mode - API accessible directly on localhost:8000
    # Traefik routing not available in host network mode
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=api,component=backend,environment=development"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  elasticsearch:
    container_name: turdpartycollab_elasticsearch
    image: docker.elastic.co/elasticsearch/elasticsearch:8.11.0
    # No direct port exposure - using Traefik routing
    expose:
      - "9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - xpack.security.enabled=false
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=elasticsearch,component=storage,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.elasticsearch.rule=Host(`elasticsearch.turdparty.localhost`)"
      - "traefik.http.routers.elasticsearch.entrypoints=web"
      - "traefik.http.services.elasticsearch.loadbalancer.server.port=9200"
      - "traefik.docker.network=traefik_network"
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:9200/_cluster/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5

  logstash:
    container_name: turdpartycollab_logstash
    image: docker.elastic.co/logstash/logstash:8.11.0
    # No direct port exposure - using Traefik routing for web interface
    expose:
      - "5000"
      - "5044"
    environment:
      - "LS_JAVA_OPTS=-Xmx256m -Xms256m"
    volumes:
      - ./config/logstash/logstash.conf:/usr/share/logstash/pipeline/logstash.conf
      - ./services/monitoring/logstash/pipeline/api-ecs-logs.conf:/usr/share/logstash/pipeline/api-ecs-logs.conf
      - ./services/monitoring/elasticsearch/templates:/usr/share/logstash/templates:ro
      - ./logs:/app/logs
    depends_on:
      - elasticsearch
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=logstash,component=logging,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.logstash.rule=Host(`logstash.turdparty.localhost`)"
      - "traefik.http.routers.logstash.entrypoints=web"
      - "traefik.http.services.logstash.loadbalancer.server.port=5000"
      - "traefik.docker.network=traefik_network"

  kibana:
    container_name: turdpartycollab_kibana
    image: docker.elastic.co/kibana/kibana:8.11.0
    # No direct port exposure - using Traefik routing
    expose:
      - "5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - xpack.security.enabled=false
    depends_on:
      - elasticsearch
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=kibana,component=visualization,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.kibana.rule=Host(`kibana.turdparty.localhost`)"
      - "traefik.http.routers.kibana.entrypoints=web"
      - "traefik.http.services.kibana.loadbalancer.server.port=5601"
      - "traefik.docker.network=traefik_network"

  # VM Monitoring Service
  vm-monitor:
    container_name: turdpartycollab_vm_monitor
    build:
      context: .
      dockerfile: docker/Dockerfile
    command: ["python", "-c", "print('VM Monitor service - placeholder for future implementation')"]
    environment:
      - PYTHONPATH=/app
      - DOCKER_HOST=unix:///var/run/docker.sock
      - MONITORING_INTERVAL=5
      - ALERT_THRESHOLDS_CPU=80
      - ALERT_THRESHOLDS_MEMORY=90
      - ELASTICSEARCH_HOST=elasticsearch
      - ELASTICSEARCH_PORT=9200
      - LOG_LEVEL=INFO
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./logs:/app/logs
      - ./api:/app/api
    depends_on:
      - elasticsearch
    networks:
      - turdpartycollab_net
    # Enable host networking for VM monitoring
    extra_hosts:
      - "host.docker.internal:host-gateway"
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=vm-monitor,component=monitoring,environment=development"

  # Status Dashboard Service
  status:
    container_name: turdpartycollab_status
    image: nginx:alpine
    volumes:
      - ./services/status:/usr/share/nginx/html:ro
      - ./services/status/nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - api
      - elasticsearch
      - kibana
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=status,component=dashboard,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.status.rule=Host(`status.turdparty.localhost`)"
      - "traefik.http.routers.status.entrypoints=web"
      - "traefik.http.services.status.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik_network"

  # Frontend Service
  frontend:
    container_name: turdpartycollab_frontend
    build:
      context: ./frontend
      dockerfile: Dockerfile
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=frontend,component=ui,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.frontend.rule=Host(`frontend.turdparty.localhost`)"
      - "traefik.http.routers.frontend.entrypoints=web"
      - "traefik.http.services.frontend.loadbalancer.server.port=3000"
      - "traefik.docker.network=traefik_network"

  # Documentation Service
  docs:
    container_name: turdpartycollab_docs
    image: nginx:alpine
    volumes:
      - ./docs/_build/html:/usr/share/nginx/html:ro
      - ./docs/nginx-docs.conf:/etc/nginx/conf.d/default.conf:ro
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=docs,component=documentation,environment=development"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.docs.rule=Host(`docs.turdparty.localhost`)"
      - "traefik.http.routers.docs.entrypoints=web"
      - "traefik.http.services.docs.loadbalancer.server.port=80"
      - "traefik.docker.network=traefik_network"

  # Filebeat for Docker log collection
  filebeat:
    container_name: turdpartycollab_filebeat
    image: docker.elastic.co/beats/filebeat:8.11.0
    user: root
    volumes:
      - ./config/filebeat/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - filebeat_data:/usr/share/filebeat/data
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
      - LOGSTASH_HOSTS=logstash:5044
    depends_on:
      - elasticsearch
      - logstash
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
        labels: "service=filebeat,component=log-collection,environment=development"
    command: ["filebeat", "-e", "-strict.perms=false"]

volumes:
  elasticsearch_data:
    name: turdpartycollab_elasticsearch_data
  filebeat_data:
    name: turdpartycollab_filebeat_data

networks:
  turdpartycollab_net:
    name: turdpartycollab_net
    driver: bridge
  traefik_network:
    name: traefik_network
    external: true
