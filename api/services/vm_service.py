"""
VM Service for managing virtual machines
"""
import logging
import asyncio
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone

from api.models.vm_management import VMCreateRequest, VMResponse, VMType, VMStatus

logger = logging.getLogger(__name__)


class VMService:
    """Service for VM lifecycle management"""
    
    def __init__(self):
        self.vms: Dict[str, Dict[str, Any]] = {}
        self.docker_client = None
        
    async def initialize(self):
        """Initialize VM service"""
        try:
            import docker
            self.docker_client = docker.from_env()
            logger.info("VM Service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Docker client: {e}")
            self.docker_client = None
    
    async def create_vm(self, request: VMCreateRequest) -> VMResponse:
        """Create a new VM"""
        try:
            vm_id = str(uuid.uuid4())
            
            # Create VM data
            vm_data = {
                "vm_id": vm_id,
                "name": request.name,
                "template": request.template,
                "vm_type": request.vm_type,
                "memory_mb": request.memory_mb,
                "cpus": request.cpus,
                "disk_gb": getattr(request, 'disk_gb', 20),
                "domain": request.domain,
                "description": getattr(request, 'description', ''),
                "status": VMStatus.CREATING,
                "created_at": datetime.now(timezone.utc).isoformat(),
                "runtime_minutes": 0.0,
                "is_expired": False
            }
            
            # Store VM
            self.vms[vm_id] = vm_data
            
            # Start VM creation process
            if request.vm_type == VMType.DOCKER:
                await self._create_docker_vm(vm_id, request)
            elif request.vm_type == VMType.VAGRANT:
                await self._create_vagrant_vm(vm_id, request)
            
            # Auto-start if requested
            if getattr(request, 'auto_start', True):
                await self._start_vm(vm_id)
            
            return VMResponse(**vm_data)
            
        except Exception as e:
            logger.error(f"Error creating VM: {e}")
            if vm_id in self.vms:
                self.vms[vm_id]["status"] = VMStatus.ERROR
            raise
    
    async def _create_docker_vm(self, vm_id: str, request: VMCreateRequest):
        """Create Docker container VM"""
        try:
            if not self.docker_client:
                await self.initialize()
            
            # Map template to Docker image
            image_map = {
                "ubuntu:20.04": "ubuntu:20.04",
                "ubuntu:22.04": "ubuntu:22.04",
                "ubuntu:18.04": "ubuntu:18.04",
                "kali:latest": "kalilinux/kali-rolling:latest",
                "centos:8": "centos:8",
                "debian:11": "debian:11"
            }
            
            image = image_map.get(request.template, "ubuntu:20.04")
            
            # Create container
            container = self.docker_client.containers.run(
                image,
                name=f"turdparty_{request.name}_{vm_id[:8]}",
                detach=True,
                mem_limit=f"{request.memory_mb}m",
                cpu_count=request.cpus,
                network="turdpartycollab_net",
                command="tail -f /dev/null",  # Keep container running
                labels={
                    "turdparty.vm_id": vm_id,
                    "turdparty.vm_name": request.name,
                    "turdparty.domain": request.domain
                }
            )
            
            # Update VM data
            vm_data = self.vms[vm_id]
            vm_data["container_id"] = container.id
            vm_data["status"] = VMStatus.RUNNING
            vm_data["started_at"] = datetime.now(timezone.utc).isoformat()
            
            # Get container IP
            container.reload()
            networks = container.attrs.get('NetworkSettings', {}).get('Networks', {})
            for network_name, network_info in networks.items():
                if network_info.get('IPAddress'):
                    vm_data["ip_address"] = network_info['IPAddress']
                    break
            
            logger.info(f"Docker VM created successfully: {vm_id}")
            
        except Exception as e:
            logger.error(f"Error creating Docker VM {vm_id}: {e}")
            self.vms[vm_id]["status"] = VMStatus.ERROR
            raise
    
    async def _create_vagrant_vm(self, vm_id: str, request: VMCreateRequest):
        """Create Vagrant VM (mock implementation)"""
        try:
            # Mock Vagrant VM creation
            vm_data = self.vms[vm_id]
            vm_data["status"] = VMStatus.STOPPED
            vm_data["vagrant_dir"] = f"/tmp/vagrant_{vm_id}"
            
            logger.info(f"Vagrant VM created (mock): {vm_id}")
            
        except Exception as e:
            logger.error(f"Error creating Vagrant VM {vm_id}: {e}")
            self.vms[vm_id]["status"] = VMStatus.ERROR
            raise
    
    async def _start_vm(self, vm_id: str):
        """Start a VM"""
        try:
            vm_data = self.vms[vm_id]
            
            if vm_data["vm_type"] == VMType.DOCKER:
                # Docker containers are started during creation
                pass
            elif vm_data["vm_type"] == VMType.VAGRANT:
                # Mock Vagrant start
                vm_data["status"] = VMStatus.RUNNING
                vm_data["started_at"] = datetime.now(timezone.utc).isoformat()
                vm_data["ip_address"] = "*************"  # Mock IP
            
            logger.info(f"VM started: {vm_id}")
            
        except Exception as e:
            logger.error(f"Error starting VM {vm_id}: {e}")
            self.vms[vm_id]["status"] = VMStatus.ERROR
            raise
    
    async def get_vm(self, vm_id: str) -> Optional[VMResponse]:
        """Get VM by ID"""
        if vm_id not in self.vms:
            return None
        
        vm_data = self.vms[vm_id].copy()
        return VMResponse(**vm_data)
    
    async def list_vms(self, skip: int = 0, limit: int = 10, status_filter: Optional[str] = None) -> List[VMResponse]:
        """List VMs with pagination and filtering"""
        vms = list(self.vms.values())
        
        # Apply status filter
        if status_filter:
            vms = [vm for vm in vms if vm["status"] == status_filter]
        
        # Apply pagination
        vms = vms[skip:skip + limit]
        
        return [VMResponse(**vm) for vm in vms]
    
    async def perform_action(self, vm_id: str, action: str, force: bool = False) -> Dict[str, Any]:
        """Perform action on VM"""
        if vm_id not in self.vms:
            raise ValueError(f"VM {vm_id} not found")
        
        vm_data = self.vms[vm_id]
        old_status = vm_data["status"]
        
        try:
            if action == "start":
                await self._start_vm(vm_id)
            elif action == "stop":
                await self._stop_vm(vm_id, force)
            elif action == "restart":
                await self._restart_vm(vm_id)
            elif action == "destroy":
                await self._destroy_vm(vm_id, force)
            elif action == "suspend":
                await self._suspend_vm(vm_id)
            elif action == "resume":
                await self._resume_vm(vm_id)
            else:
                raise ValueError(f"Invalid action: {action}")
            
            return {
                "vm_id": vm_id,
                "action": action,
                "old_status": old_status,
                "new_status": vm_data["status"],
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error performing action {action} on VM {vm_id}: {e}")
            vm_data["status"] = VMStatus.ERROR
            raise
    
    async def _stop_vm(self, vm_id: str, force: bool = False):
        """Stop a VM"""
        vm_data = self.vms[vm_id]
        
        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                if force:
                    container.kill()
                else:
                    container.stop()
        
        vm_data["status"] = VMStatus.STOPPED
        vm_data["stopped_at"] = datetime.now(timezone.utc).isoformat()
    
    async def _restart_vm(self, vm_id: str):
        """Restart a VM"""
        await self._stop_vm(vm_id)
        await asyncio.sleep(1)  # Brief pause
        await self._start_vm(vm_id)
    
    async def _destroy_vm(self, vm_id: str, force: bool = False):
        """Destroy a VM"""
        vm_data = self.vms[vm_id]
        
        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                if container.status == "running":
                    container.kill() if force else container.stop()
                container.remove()
        
        vm_data["status"] = VMStatus.DESTROYED
        vm_data["destroyed_at"] = datetime.now(timezone.utc).isoformat()
    
    async def _suspend_vm(self, vm_id: str):
        """Suspend a VM"""
        vm_data = self.vms[vm_id]
        
        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                container.pause()
        
        vm_data["status"] = VMStatus.SUSPENDED
        vm_data["suspended_at"] = datetime.now(timezone.utc).isoformat()
    
    async def _resume_vm(self, vm_id: str):
        """Resume a VM"""
        vm_data = self.vms[vm_id]
        
        if vm_data["vm_type"] == VMType.DOCKER:
            if "container_id" in vm_data:
                container = self.docker_client.containers.get(vm_data["container_id"])
                container.unpause()
        
        vm_data["status"] = VMStatus.RUNNING
        vm_data["resumed_at"] = datetime.now(timezone.utc).isoformat()
    
    async def delete_vm(self, vm_id: str, force: bool = False):
        """Delete a VM"""
        if vm_id not in self.vms:
            raise ValueError(f"VM {vm_id} not found")
        
        vm_data = self.vms[vm_id]
        
        # Stop/destroy VM first if running
        if vm_data["status"] in [VMStatus.RUNNING, VMStatus.SUSPENDED]:
            if not force:
                raise ValueError("Cannot delete running VM without force=true")
            await self._destroy_vm(vm_id, force=True)
        
        # Remove from storage
        del self.vms[vm_id]
        
        logger.info(f"VM deleted: {vm_id}")


# Global instance
vm_service = VMService()
