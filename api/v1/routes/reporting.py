"""
TurdParty API Reporting Endpoints
Comprehensive binary execution reporting with ECS data integration.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from uuid import UUID
import json

from fastapi import APIRouter, HTTPException, Query, Depends, status
from pydantic import BaseModel, Field
import httpx
from elasticsearch import AsyncElasticsearch

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/reports",
    tags=["reporting"],
    responses={404: {"description": "Report not found"}},
)

# Pydantic models for request/response
class BinaryReportSummary(BaseModel):
    """Summary information for a binary execution report."""
    report_id: str
    file_uuid: str
    filename: str
    file_size_bytes: int
    execution_status: str
    total_executions: int
    last_execution: Optional[datetime]
    risk_level: str
    generated_at: datetime

class InstallationFootprint(BaseModel):
    """Installation footprint analysis."""
    files_created: int
    files_modified: int
    directories_created: int
    registry_keys_created: int
    services_installed: int
    total_disk_usage_mb: float
    installation_paths: List[str]

class RuntimeBehavior(BaseModel):
    """Runtime behavior analysis."""
    execution_duration_seconds: float
    processes_spawned: int
    network_connections: int
    peak_cpu_percent: float
    peak_memory_mb: float
    exit_code: int

class SecurityAnalysis(BaseModel):
    """Security analysis results."""
    threat_score: int = Field(..., ge=0, le=10)
    risk_level: str
    suspicious_indicators: List[str]
    behavioral_patterns: List[str]
    digital_signature_valid: bool

class BinaryReport(BaseModel):
    """Complete binary execution report."""
    metadata: Dict[str, Any]
    file_info: Dict[str, Any]
    execution_summary: Dict[str, Any]
    installation_footprint: Dict[str, Any]
    runtime_behavior: Dict[str, Any]
    security_analysis: Dict[str, Any]
    vm_environment: Dict[str, Any]
    ecs_data_summary: Dict[str, Any]
    artifacts: Dict[str, Any]

class ReportSearchQuery(BaseModel):
    """Search query for binary reports."""
    filename: Optional[str] = None
    file_hash: Optional[str] = None
    risk_level: Optional[str] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    vm_template: Optional[str] = None
    limit: int = Field(default=50, le=1000)
    offset: int = Field(default=0, ge=0)

class CompareRequest(BaseModel):
    """Request to compare multiple binary executions."""
    file_uuids: List[str] = Field(..., min_items=2, max_items=10)
    comparison_fields: List[str] = Field(default=["installation_footprint", "runtime_behavior", "security_analysis"])

# Elasticsearch client configuration
async def get_elasticsearch_client():
    """Get configured Elasticsearch client."""
    return AsyncElasticsearch(
        hosts=["http://elasticsearch.turdparty.localhost:9200"],
        timeout=30,
        max_retries=3
    )

class ReportingService:
    """Service for generating comprehensive binary execution reports."""
    
    def __init__(self):
        self.es_client = None
        self.minio_base_url = "http://minio.turdparty.localhost"
    
    async def initialize(self):
        """Initialize the reporting service."""
        self.es_client = await get_elasticsearch_client()
    
    async def generate_binary_report(self, file_uuid: str) -> Dict[str, Any]:
        """Generate comprehensive report for a binary execution."""
        try:
            # Gather data from multiple sources
            file_metadata = await self._get_file_metadata(file_uuid)
            execution_data = await self._get_execution_data(file_uuid)
            vm_data = await self._get_vm_data(file_uuid)
            ecs_logs = await self._get_ecs_logs(file_uuid)
            
            # Analyze and aggregate data
            installation_footprint = await self._analyze_installation_footprint(ecs_logs)
            runtime_behavior = await self._analyze_runtime_behavior(ecs_logs, execution_data)
            security_analysis = await self._perform_security_analysis(file_metadata, ecs_logs)
            
            # Generate comprehensive report
            report = {
                "metadata": {
                    "report_id": f"rpt_{file_uuid}",
                    "file_uuid": file_uuid,
                    "generated_at": datetime.utcnow().isoformat(),
                    "report_version": "1.0",
                    "data_sources": ["elasticsearch", "minio"],
                    "analysis_duration_seconds": 0.0  # Will be calculated
                },
                "file_info": file_metadata,
                "execution_summary": await self._generate_execution_summary(execution_data),
                "installation_footprint": installation_footprint,
                "runtime_behavior": runtime_behavior,
                "security_analysis": security_analysis,
                "vm_environment": vm_data,
                "ecs_data_summary": await self._summarize_ecs_data(ecs_logs),
                "artifacts": await self._get_artifacts(file_uuid)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"Error generating report for {file_uuid}: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to generate report: {str(e)}"
            )
    
    async def _get_file_metadata(self, file_uuid: str) -> Dict[str, Any]:
        """Retrieve file metadata from Elasticsearch."""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"file_uuid.keyword": file_uuid}},
                        {"term": {"event.action.keyword": "file_upload"}}
                    ]
                }
            },
            "size": 1,
            "sort": [{"@timestamp": {"order": "desc"}}]
        }
        
        result = await self.es_client.search(
            index="turdparty-files-*",
            body=query
        )
        
        if result["hits"]["total"]["value"] > 0:
            source = result["hits"]["hits"][0]["_source"]
            return {
                "filename": source.get("file", {}).get("name", "unknown"),
                "file_size_bytes": source.get("file", {}).get("size", 0),
                "file_type": source.get("file", {}).get("type", "unknown"),
                "hashes": {
                    "blake3": source.get("file", {}).get("hash", {}).get("blake3", ""),
                    "sha256": source.get("file", {}).get("hash", {}).get("sha256", ""),
                    "md5": source.get("file", {}).get("hash", {}).get("md5", "")
                },
                "upload_timestamp": source.get("@timestamp"),
                "minio_location": source.get("minio", {}).get("object_key", "")
            }
        
        return {"error": "File metadata not found"}
    
    async def _get_execution_data(self, file_uuid: str) -> List[Dict[str, Any]]:
        """Retrieve execution data from Elasticsearch."""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"file_uuid.keyword": file_uuid}},
                        {"terms": {"event.action.keyword": ["vm_creation", "file_injection", "application_installation"]}}
                    ]
                }
            },
            "size": 100,
            "sort": [{"@timestamp": {"order": "asc"}}]
        }
        
        result = await self.es_client.search(
            index="turdparty-vm-*",
            body=query
        )
        
        return [hit["_source"] for hit in result["hits"]["hits"]]
    
    async def _get_vm_data(self, file_uuid: str) -> Dict[str, Any]:
        """Retrieve VM environment data."""
        query = {
            "query": {
                "bool": {
                    "must": [
                        {"term": {"file_uuid.keyword": file_uuid}},
                        {"term": {"event.action.keyword": "vm_creation"}}
                    ]
                }
            },
            "size": 1,
            "sort": [{"@timestamp": {"order": "desc"}}]
        }
        
        result = await self.es_client.search(
            index="turdparty-vm-*",
            body=query
        )
        
        if result["hits"]["total"]["value"] > 0:
            source = result["hits"]["hits"][0]["_source"]
            return {
                "vm_id": source.get("vm_id", "unknown"),
                "vm_template": source.get("vm_template", "unknown"),
                "vm_configuration": source.get("vm_config", {}),
                "execution_environment": source.get("execution_env", {}),
                "vm_lifecycle": source.get("vm_lifecycle", {})
            }
        
        return {"error": "VM data not found"}
    
    async def _get_ecs_logs(self, file_uuid: str) -> List[Dict[str, Any]]:
        """Retrieve all ECS logs for the file UUID."""
        query = {
            "query": {
                "term": {"file_uuid.keyword": file_uuid}
            },
            "size": 10000,
            "sort": [{"@timestamp": {"order": "asc"}}]
        }
        
        result = await self.es_client.search(
            index="turdparty-*",
            body=query
        )
        
        return [hit["_source"] for hit in result["hits"]["hits"]]
    
    async def _analyze_installation_footprint(self, ecs_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze installation footprint from ECS logs."""
        filesystem_changes = {"files_created": 0, "files_modified": 0, "directories_created": 0}
        registry_changes = {"keys_created": 0, "keys_modified": 0}
        services_installed = []
        installation_paths = set()
        
        for log in ecs_logs:
            event_action = log.get("event", {}).get("action", "")
            
            if event_action == "file_created":
                filesystem_changes["files_created"] += 1
                file_path = log.get("file", {}).get("path", "")
                if file_path:
                    installation_paths.add(file_path.split("\\")[0:3])  # Get base installation path
            
            elif event_action == "file_modified":
                filesystem_changes["files_modified"] += 1
            
            elif event_action == "directory_created":
                filesystem_changes["directories_created"] += 1
            
            elif event_action == "registry_key_created":
                registry_changes["keys_created"] += 1
            
            elif event_action == "service_installed":
                services_installed.append({
                    "service_name": log.get("service", {}).get("name", ""),
                    "display_name": log.get("service", {}).get("display_name", ""),
                    "start_type": log.get("service", {}).get("start_type", ""),
                    "status": log.get("service", {}).get("status", "")
                })
        
        return {
            "filesystem_changes": filesystem_changes,
            "registry_changes": registry_changes,
            "services_installed": services_installed,
            "installation_paths": list(installation_paths),
            "total_disk_usage_mb": sum(log.get("file", {}).get("size", 0) for log in ecs_logs if log.get("event", {}).get("action") == "file_created") / (1024 * 1024)
        }
    
    async def _analyze_runtime_behavior(self, ecs_logs: List[Dict[str, Any]], execution_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze runtime behavior from logs and execution data."""
        processes = []
        network_connections = 0
        peak_cpu = 0.0
        peak_memory = 0.0
        execution_duration = 0.0
        
        for log in ecs_logs:
            event_action = log.get("event", {}).get("action", "")
            
            if event_action == "process_start":
                processes.append({
                    "process_name": log.get("process", {}).get("name", ""),
                    "pid": log.get("process", {}).get("pid", 0),
                    "command_line": log.get("process", {}).get("command_line", ""),
                    "start_time": log.get("@timestamp")
                })
            
            elif event_action == "network_connection":
                network_connections += 1
            
            # Track resource usage
            cpu_percent = log.get("system", {}).get("cpu", {}).get("percent", 0)
            memory_mb = log.get("system", {}).get("memory", {}).get("used_mb", 0)
            
            peak_cpu = max(peak_cpu, cpu_percent)
            peak_memory = max(peak_memory, memory_mb)
        
        # Calculate execution duration from execution data
        if execution_data:
            start_times = [log.get("@timestamp") for log in execution_data if log.get("event", {}).get("action") == "application_installation"]
            end_times = [log.get("@timestamp") for log in execution_data if log.get("event", {}).get("action") == "installation_complete"]
            
            if start_times and end_times:
                # Simple duration calculation (would need proper datetime parsing in real implementation)
                execution_duration = 45.7  # Mock value
        
        return {
            "process_execution": {
                "total_processes_spawned": len(processes),
                "main_process": processes[0] if processes else {},
                "child_processes": processes[1:] if len(processes) > 1 else []
            },
            "network_activity": {
                "connections_established": network_connections,
                "dns_queries": [log.get("dns", {}).get("question", {}).get("name") for log in ecs_logs if log.get("event", {}).get("action") == "dns_query"],
                "external_ips_contacted": list(set(log.get("destination", {}).get("ip") for log in ecs_logs if log.get("event", {}).get("action") == "network_connection"))
            },
            "resource_usage": {
                "peak_cpu_percent": peak_cpu,
                "peak_memory_mb": peak_memory,
                "execution_duration_seconds": execution_duration
            }
        }
    
    async def _perform_security_analysis(self, file_metadata: Dict[str, Any], ecs_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Perform security analysis based on file metadata and behavior."""
        threat_score = 0
        risk_level = "low"
        indicators = []
        behavioral_patterns = []
        
        # Analyze suspicious behaviors
        for log in ecs_logs:
            event_action = log.get("event", {}).get("action", "")
            
            if event_action == "registry_modification" and "Run" in log.get("registry", {}).get("key", ""):
                threat_score += 1
                indicators.append("Modifies startup registry keys")
                behavioral_patterns.append("persistence_mechanism")
            
            if event_action == "network_connection" and log.get("destination", {}).get("port") in [80, 443]:
                indicators.append("Makes HTTP/HTTPS connections")
            
            if event_action == "process_injection":
                threat_score += 3
                indicators.append("Performs process injection")
                behavioral_patterns.append("code_injection")
        
        # Determine risk level
        if threat_score >= 5:
            risk_level = "high"
        elif threat_score >= 2:
            risk_level = "medium"
        
        return {
            "threat_indicators": {
                "suspicious_behavior_score": threat_score,
                "risk_level": risk_level,
                "indicators": [{"type": "behavior", "description": ind, "severity": "medium"} for ind in indicators]
            },
            "behavioral_patterns": {
                "patterns": behavioral_patterns,
                "installation_behavior": "standard_installer",
                "persistence_mechanisms": ["startup_registry"] if "persistence_mechanism" in behavioral_patterns else [],
                "privilege_escalation": False,
                "code_injection": "code_injection" in behavioral_patterns,
                "anti_analysis": False
            },
            "file_reputation": {
                "known_good": threat_score == 0,
                "digital_signature": {
                    "signed": True,  # Would check actual signature
                    "valid": True,
                    "signer": "Unknown"
                }
            }
        }
    
    async def _generate_execution_summary(self, execution_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate execution summary from execution data."""
        return {
            "total_executions": len([log for log in execution_data if log.get("event", {}).get("action") == "application_installation"]),
            "successful_executions": len([log for log in execution_data if log.get("event", {}).get("action") == "installation_complete"]),
            "failed_executions": 0,  # Would calculate from error logs
            "last_execution": execution_data[-1].get("@timestamp") if execution_data else None,
            "execution_status": "completed"
        }
    
    async def _summarize_ecs_data(self, ecs_logs: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Summarize ECS data collection."""
        event_categories = {}
        log_sources = set()
        
        for log in ecs_logs:
            category = log.get("event", {}).get("category", ["unknown"])[0] if isinstance(log.get("event", {}).get("category"), list) else log.get("event", {}).get("category", "unknown")
            event_categories[category] = event_categories.get(category, 0) + 1
            
            source = log.get("service", {}).get("name", "unknown")
            log_sources.add(source)
        
        return {
            "total_log_entries": len(ecs_logs),
            "log_sources": list(log_sources),
            "event_categories": event_categories,
            "data_collection_period": {
                "start": ecs_logs[0].get("@timestamp") if ecs_logs else None,
                "end": ecs_logs[-1].get("@timestamp") if ecs_logs else None,
                "duration_seconds": 45  # Would calculate actual duration
            }
        }
    
    async def _get_artifacts(self, file_uuid: str) -> Dict[str, Any]:
        """Get artifacts (screenshots, memory dumps, logs) for the execution."""
        return {
            "screenshots": [],  # Would query MinIO for screenshots
            "memory_dumps": [],  # Would query MinIO for memory dumps
            "log_files": []     # Would query MinIO for log files
        }

# Initialize reporting service
reporting_service = ReportingService()

@router.on_event("startup")
async def startup_event():
    """Initialize reporting service on startup."""
    await reporting_service.initialize()

# API Endpoints
@router.get("/binary/{file_uuid}", response_model=BinaryReport)
async def get_binary_report(file_uuid: str):
    """
    Get comprehensive execution report for a binary by UUID.
    
    Returns detailed analysis including:
    - Installation footprint (files, registry, services)
    - Runtime behavior (processes, network, resources)
    - Security analysis (threat indicators, behavioral patterns)
    - VM environment details
    - ECS data aggregation
    """
    try:
        report = await reporting_service.generate_binary_report(file_uuid)
        return report
    except Exception as e:
        logger.error(f"Error generating report for {file_uuid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate report: {str(e)}"
        )

@router.get("/binary/{file_uuid}/summary", response_model=BinaryReportSummary)
async def get_binary_summary(file_uuid: str):
    """Get summary information for a binary execution."""
    try:
        full_report = await reporting_service.generate_binary_report(file_uuid)
        
        return BinaryReportSummary(
            report_id=full_report["metadata"]["report_id"],
            file_uuid=file_uuid,
            filename=full_report["file_info"]["filename"],
            file_size_bytes=full_report["file_info"]["file_size_bytes"],
            execution_status=full_report["execution_summary"]["execution_status"],
            total_executions=full_report["execution_summary"]["total_executions"],
            last_execution=full_report["execution_summary"]["last_execution"],
            risk_level=full_report["security_analysis"]["threat_indicators"]["risk_level"],
            generated_at=datetime.utcnow()
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Binary report not found: {str(e)}"
        )

@router.get("/binary/{file_uuid}/footprint", response_model=InstallationFootprint)
async def get_installation_footprint(file_uuid: str):
    """Get detailed installation footprint analysis."""
    try:
        full_report = await reporting_service.generate_binary_report(file_uuid)
        footprint = full_report["installation_footprint"]
        
        return InstallationFootprint(
            files_created=footprint["filesystem_changes"]["files_created"],
            files_modified=footprint["filesystem_changes"]["files_modified"],
            directories_created=footprint["filesystem_changes"]["directories_created"],
            registry_keys_created=footprint["registry_changes"]["keys_created"],
            services_installed=len(footprint["services_installed"]),
            total_disk_usage_mb=footprint["total_disk_usage_mb"],
            installation_paths=footprint["installation_paths"]
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Installation footprint not found: {str(e)}"
        )

@router.get("/binary/{file_uuid}/runtime", response_model=RuntimeBehavior)
async def get_runtime_behavior(file_uuid: str):
    """Get detailed runtime behavior analysis."""
    try:
        full_report = await reporting_service.generate_binary_report(file_uuid)
        runtime = full_report["runtime_behavior"]
        
        return RuntimeBehavior(
            execution_duration_seconds=runtime["resource_usage"]["execution_duration_seconds"],
            processes_spawned=runtime["process_execution"]["total_processes_spawned"],
            network_connections=runtime["network_activity"]["connections_established"],
            peak_cpu_percent=runtime["resource_usage"]["peak_cpu_percent"],
            peak_memory_mb=runtime["resource_usage"]["peak_memory_mb"],
            exit_code=0  # Would extract from process data
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Runtime behavior not found: {str(e)}"
        )

@router.post("/search")
async def search_reports(query: ReportSearchQuery):
    """
    Search binary reports with flexible filtering.
    
    Supports filtering by:
    - Filename patterns
    - File hashes
    - Risk levels
    - Date ranges
    - VM templates
    """
    try:
        # Build Elasticsearch query based on search parameters
        es_query = {"query": {"bool": {"must": []}}}
        
        if query.filename:
            es_query["query"]["bool"]["must"].append({
                "wildcard": {"file.name": f"*{query.filename}*"}
            })
        
        if query.file_hash:
            es_query["query"]["bool"]["should"] = [
                {"term": {"file.hash.blake3.keyword": query.file_hash}},
                {"term": {"file.hash.sha256.keyword": query.file_hash}},
                {"term": {"file.hash.md5.keyword": query.file_hash}}
            ]
            es_query["query"]["bool"]["minimum_should_match"] = 1
        
        if query.date_from or query.date_to:
            date_range = {}
            if query.date_from:
                date_range["gte"] = query.date_from.isoformat()
            if query.date_to:
                date_range["lte"] = query.date_to.isoformat()
            
            es_query["query"]["bool"]["must"].append({
                "range": {"@timestamp": date_range}
            })
        
        es_query["size"] = query.limit
        es_query["from"] = query.offset
        es_query["sort"] = [{"@timestamp": {"order": "desc"}}]
        
        # Execute search
        result = await reporting_service.es_client.search(
            index="turdparty-files-*",
            body=es_query
        )
        
        # Generate summary reports for each result
        reports = []
        for hit in result["hits"]["hits"]:
            source = hit["_source"]
            file_uuid = source.get("file_uuid")
            
            if file_uuid:
                try:
                    summary_report = await get_binary_summary(file_uuid)
                    reports.append(summary_report)
                except:
                    # Skip if report generation fails
                    continue
        
        return {
            "total": result["hits"]["total"]["value"],
            "reports": reports,
            "query": query.dict()
        }
        
    except Exception as e:
        logger.error(f"Error searching reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )

@router.post("/compare")
async def compare_binaries(request: CompareRequest):
    """
    Compare execution behavior across multiple binaries.
    
    Generates comparative analysis of:
    - Installation footprints
    - Runtime behaviors
    - Security profiles
    """
    try:
        comparison_data = {}
        
        for file_uuid in request.file_uuids:
            try:
                report = await reporting_service.generate_binary_report(file_uuid)
                
                # Extract requested comparison fields
                binary_data = {}
                for field in request.comparison_fields:
                    if field in report:
                        binary_data[field] = report[field]
                
                comparison_data[file_uuid] = {
                    "filename": report["file_info"]["filename"],
                    "data": binary_data
                }
                
            except Exception as e:
                logger.warning(f"Failed to get report for {file_uuid}: {e}")
                comparison_data[file_uuid] = {"error": str(e)}
        
        # Generate comparison analysis
        comparison_analysis = {
            "summary": {
                "binaries_compared": len(request.file_uuids),
                "successful_reports": len([data for data in comparison_data.values() if "error" not in data]),
                "comparison_fields": request.comparison_fields,
                "generated_at": datetime.utcnow().isoformat()
            },
            "data": comparison_data,
            "insights": {
                "common_behaviors": [],  # Would analyze common patterns
                "unique_behaviors": [],  # Would identify unique behaviors
                "risk_comparison": {}    # Would compare risk levels
            }
        }
        
        return comparison_analysis
        
    except Exception as e:
        logger.error(f"Error comparing binaries: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Comparison failed: {str(e)}"
        )

@router.get("/health")
async def reporting_health():
    """Health check for reporting service."""
    try:
        # Test Elasticsearch connection
        es_health = await reporting_service.es_client.cluster.health()
        
        return {
            "status": "healthy",
            "elasticsearch": {
                "status": es_health["status"],
                "cluster_name": es_health["cluster_name"],
                "number_of_nodes": es_health["number_of_nodes"]
            },
            "service": "turdparty-reporting",
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }
