"""
FastAPI application factory
"""
import logging
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import HTMLResponse
from api.v1.routes import file_injection, files, vm_injection, template_injection, vm_management, vm_files, reporting
from api.middleware.ecs_logging import ECSLoggingMiddleware

logger = logging.getLogger(__name__)

def get_redoc_html() -> str:
    """Generate custom ReDoc HTML with dark mode support"""
    return """
    <!DOCTYPE html>
    <html data-theme="dark">
    <head>
        <title>TurdParty API Documentation</title>
        <meta charset="utf-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <link href="https://fonts.googleapis.com/css?family=Montserrat:300,400,700|Roboto:300,400,700" rel="stylesheet">
        <style>
            :root {
                --bg-primary: #1a1a1a;
                --bg-secondary: #2d2d2d;
                --bg-tertiary: #3a3a3a;
                --text-primary: #e0e0e0;
                --text-secondary: #b0b0b0;
                --border-color: #444444;
                --accent-primary: #3498DB;
                --accent-secondary: #2ECC71;
            }

            body {
                margin: 0;
                padding: 0;
                background: var(--bg-primary);
                color: var(--text-primary);
            }

            /* Theme toggle button */
            .theme-toggle {
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 1000;
                background: var(--accent-primary);
                color: white;
                border: none;
                border-radius: 20px;
                padding: 8px 16px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
            }

            .theme-toggle:hover {
                background: var(--accent-secondary);
                transform: scale(1.05);
            }

            /* Light theme override */
            [data-theme="light"] {
                --bg-primary: #ffffff;
                --bg-secondary: #f8f9fa;
                --bg-tertiary: #e9ecef;
                --text-primary: #212529;
                --text-secondary: #495057;
                --border-color: #dee2e6;
            }

            [data-theme="light"] body {
                background: var(--bg-primary);
                color: var(--text-primary);
            }
        </style>
    </head>
    <body>
        <button id="theme-toggle" class="theme-toggle">🌙 Dark</button>
        <redoc spec-url="/openapi.json"
               theme='{
                   "colors": {
                       "primary": {"main": "#3498DB"},
                       "success": {"main": "#2ECC71"},
                       "warning": {"main": "#F39C12"},
                       "error": {"main": "#E74C3C"},
                       "text": {"primary": "#e0e0e0", "secondary": "#b0b0b0"},
                       "border": {"dark": "#444444", "light": "#666666"}
                   },
                   "typography": {
                       "fontSize": "14px",
                       "lineHeight": "1.5em",
                       "code": {"fontSize": "13px", "fontFamily": "Courier, monospace"},
                       "headings": {"fontFamily": "Montserrat, sans-serif"}
                   },
                   "sidebar": {
                       "backgroundColor": "#2d2d2d",
                       "textColor": "#e0e0e0",
                       "activeTextColor": "#3498DB"
                   },
                   "rightPanel": {
                       "backgroundColor": "#1a1a1a",
                       "textColor": "#e0e0e0"
                   },
                   "codeBlock": {
                       "backgroundColor": "#1e1e1e"
                   }
               }'
               options='{"theme": "dark", "scrollYOffset": 60, "hideDownloadButton": false}'></redoc>
        <script src="https://cdn.jsdelivr.net/npm/redoc@2.1.3/bundles/redoc.standalone.js"></script>

        <script>
            // Theme toggle functionality
            const themeToggle = document.getElementById('theme-toggle');
            const html = document.documentElement;

            let currentTheme = localStorage.getItem('redoc-theme') || 'dark';
            html.setAttribute('data-theme', currentTheme);
            updateToggleButton();

            themeToggle.addEventListener('click', () => {
                currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
                html.setAttribute('data-theme', currentTheme);
                localStorage.setItem('redoc-theme', currentTheme);
                updateToggleButton();

                // Update ReDoc theme dynamically
                updateRedocTheme();
            });

            function updateRedocTheme() {
                const redocContainer = document.querySelector('redoc');
                if (redocContainer) {
                    // Update CSS variables for dynamic theming
                    if (currentTheme === 'light') {
                        document.documentElement.style.setProperty('--bg-primary', '#ffffff');
                        document.documentElement.style.setProperty('--bg-secondary', '#f8f9fa');
                        document.documentElement.style.setProperty('--text-primary', '#212529');
                    } else {
                        document.documentElement.style.setProperty('--bg-primary', '#1a1a1a');
                        document.documentElement.style.setProperty('--bg-secondary', '#2d2d2d');
                        document.documentElement.style.setProperty('--text-primary', '#e0e0e0');
                    }
                }
            }

            function updateToggleButton() {
                themeToggle.textContent = currentTheme === 'dark' ? '☀️ Light' : '🌙 Dark';
            }
        </script>
    </body>
    </html>
    """

def get_application() -> FastAPI:
    """Create and configure FastAPI application"""
    
    app = FastAPI(
        title="TurdParty API",
        description="VM Management and File Injection Platform",
        version="1.0.0",
        docs_url="/docs",
        redoc_url=None  # We'll create a custom ReDoc endpoint
    )
    
    # Add ECS logging middleware (before CORS)
    app.add_middleware(ECSLoggingMiddleware, log_level="INFO")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Custom ReDoc endpoint with dark mode
    @app.get("/redoc", response_class=HTMLResponse)
    async def redoc_html():
        """Custom ReDoc documentation with dark mode support"""
        return HTMLResponse(content=get_redoc_html(), media_type="text/html")

    # Health check endpoint
    @app.get("/health")
    async def health_check():
        """Health check endpoint"""
        return {
            "status": "healthy",
            "service": "turdparty-api",
            "version": "1.0.0"
        }

    # Additional health endpoints for status dashboard
    @app.get("/api/v1/health/elasticsearch")
    async def elasticsearch_health():
        """Proxy health check for Elasticsearch."""
        try:
            import httpx
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get("http://elasticsearch:9200/_cluster/health")
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "status": data.get("status", "unknown"),
                        "message": f"Cluster status: {data.get('status', 'unknown')}",
                        "number_of_nodes": data.get("number_of_nodes", 0),
                        "details": data
                    }
                else:
                    return {
                        "status": "outage",
                        "message": f"HTTP {response.status_code}: {response.text}"
                    }
        except Exception as e:
            return {
                "status": "outage",
                "message": f"Connection failed: {str(e)}"
            }

    @app.get("/api/v1/health/minio")
    async def minio_health():
        """Proxy health check for MinIO."""
        return {
            "status": "outage",
            "message": "MinIO service not configured in current setup"
        }

    @app.get("/api/v1/health/kibana")
    async def kibana_health():
        """Proxy health check for Kibana."""
        try:
            import httpx
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get("http://kibana:5601/api/status")
                if response.status_code == 200:
                    data = response.json()
                    overall_state = data.get("status", {}).get("overall", {}).get("state", "unknown")
                    return {
                        "status": {
                            "overall": {
                                "state": overall_state
                            }
                        },
                        "message": f"Kibana status: {overall_state}",
                        "details": data
                    }
                else:
                    return {
                        "status": "outage",
                        "message": f"HTTP {response.status_code}: Kibana unavailable"
                    }
        except Exception as e:
            return {
                "status": "outage",
                "message": f"Connection failed: {str(e)}"
            }

    @app.get("/api/v1/health/redis")
    async def redis_health():
        """Health check for Redis cache."""
        return {
            "status": "outage",
            "message": "Redis service not configured in current setup"
        }

    @app.get("/api/v1/health/database")
    async def database_health():
        """Health check for PostgreSQL database."""
        return {
            "status": "outage",
            "message": "Database service not configured in current setup"
        }

    @app.get("/api/v1/health/logstash")
    async def logstash_health():
        """Proxy health check for Logstash."""
        try:
            import httpx
            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get("http://logstash:9600/_node/stats")
                if response.status_code == 200:
                    data = response.json()
                    return {
                        "status": "healthy",
                        "message": "Data processing pipeline healthy",
                        "details": {
                            "pipeline": data.get("pipeline", {}),
                            "jvm": data.get("jvm", {})
                        }
                    }
                else:
                    return {
                        "status": "outage",
                        "message": f"HTTP {response.status_code}: Logstash unavailable"
                    }
        except Exception as e:
            return {
                "status": "outage",
                "message": f"Connection failed: {str(e)}"
            }

    @app.get("/api/v1/health/vm-monitor")
    async def vm_monitor_health():
        """Health check for VM monitoring agent."""
        return {
            "status": "healthy",
            "message": "VM monitoring agent operational"
        }

    # JavaScript Error Logging Endpoint
    @app.post("/api/v1/logs/ui-error")
    async def log_ui_errors(request: Request):
        """Log JavaScript errors from the frontend to Elasticsearch."""
        try:
            import httpx
            from datetime import datetime

            # Get the request body
            body = await request.json()
            errors = body.get('errors', [])
            metadata = body.get('metadata', {})

            # Process each error
            processed_errors = []
            for error in errors:
                # Create ECS-compliant log entry
                ecs_entry = {
                    "@timestamp": error.get('timestamp', datetime.utcnow().isoformat() + 'Z'),
                    "ecs": {"version": "8.11.0"},
                    "event": {
                        "kind": "event",
                        "category": ["web"],
                        "type": ["error"],
                        "outcome": "failure"
                    },
                    "service": {
                        "name": "turdparty-status-dashboard",
                        "type": "web",
                        "version": "1.0.0"
                    },
                    "error": {
                        "message": error.get('message', 'Unknown error'),
                        "type": error.get('type', 'javascript_error'),
                        "stack_trace": error.get('stack'),
                        "code": error.get('source')
                    },
                    "url": {
                        "full": error.get('url'),
                        "path": error.get('url', '').split('?')[0] if error.get('url') else None
                    },
                    "user_agent": {
                        "original": error.get('userAgent')
                    },
                    "client": {
                        "ip": request.client.host if request.client else None
                    },
                    "labels": {
                        "session_id": error.get('sessionId'),
                        "component": error.get('component', 'status-dashboard'),
                        "environment": error.get('environment', 'unknown'),
                        "severity": error.get('severity', 'error')
                    },
                    "javascript": {
                        "error": {
                            "line": error.get('line'),
                            "column": error.get('column'),
                            "source": error.get('source'),
                            "reason": error.get('reason')
                        },
                        "viewport": error.get('viewport'),
                        "performance": error.get('performance'),
                        "memory": error.get('memoryUsage')
                    },
                    "tags": ["javascript", "frontend", "status-dashboard", "turdparty"]
                }

                processed_errors.append(ecs_entry)

            # Send to Elasticsearch via Logstash
            try:
                async with httpx.AsyncClient(timeout=10.0) as client:
                    for error_entry in processed_errors:
                        # Send to Logstash HTTP input
                        response = await client.post(
                            "http://logstash:8080",
                            json=error_entry,
                            headers={"Content-Type": "application/json"}
                        )

                        if response.status_code != 200:
                            print(f"Failed to send error to Logstash: {response.status_code}")

                return {
                    "status": "success",
                    "message": f"Logged {len(processed_errors)} errors",
                    "timestamp": datetime.utcnow().isoformat() + 'Z'
                }

            except Exception as e:
                print(f"Error sending to Logstash: {str(e)}")
                # Fallback: at least log to console/file
                for error in processed_errors:
                    print(f"JS Error: {error}")

                return {
                    "status": "partial_success",
                    "message": f"Logged {len(processed_errors)} errors locally",
                    "error": str(e)
                }

        except Exception as e:
            print(f"Error processing UI error logs: {str(e)}")
            return {
                "status": "error",
                "message": "Failed to process error logs",
                "error": str(e)
            }
    
    # Include routers
    app.include_router(
        file_injection.router,
        prefix="/api/v1",
        tags=["file_injection"]
    )

    app.include_router(
        files.router,
        prefix="/api/v1",
        tags=["files"]
    )

    app.include_router(
        vm_injection.router,
        prefix="/api/v1",
        tags=["vm_injection"]
    )

    app.include_router(
        template_injection.router,
        prefix="/api/v1",
        tags=["template_injection"]
    )

    app.include_router(
        vm_management.router,
        prefix="/api/v1",
        tags=["vm_management"]
    )

    app.include_router(
        vm_files.router,
        prefix="/api/v1",
        tags=["vm_files"]
    )

    app.include_router(
        reporting.router,
        prefix="/api/v1",
        tags=["reporting"]
    )

    logger.info("FastAPI application configured successfully")
    return app
