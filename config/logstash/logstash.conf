input {
  # TCP input for direct application logs
  tcp {
    port => 5000
    codec => json_lines
    tags => ["tcp_logs"]
  }

  # Beats input for Filebeat (Docker container logs)
  beats {
    port => 5044
    tags => ["docker_logs"]
  }

  # File input for log files
  file {
    path => "/app/logs/*.log"
    start_position => "beginning"
    sincedb_path => "/dev/null"
    codec => "json"
    tags => ["file_logs"]
  }
}

filter {
  # Process Docker container logs from Filebeat
  if "docker_logs" in [tags] {
    # Parse Docker JSON log format
    if [message] {
      json {
        source => "message"
        target => "docker"
      }
    }

    # Extract container information
    if [container][name] {
      mutate {
        add_field => { "service_name" => "%{[container][name]}" }
        add_field => { "container_id" => "%{[container][id]}" }
        add_field => { "container_image" => "%{[container][image][name]}" }
      }

      # Clean up service name (remove turdpartycollab_ prefix)
      mutate {
        gsub => [ "service_name", "turdpartycollab_", "" ]
      }
    }

    # Use log field as main message for Dock<PERSON> logs
    if [log] {
      mutate {
        rename => { "log" => "message" }
      }
    }

    # Add Docker-specific fields
    mutate {
      add_field => { "log_source" => "docker" }
      add_field => { "platform" => "turdparty" }
    }
  }

  # Parse timestamp
  if [timestamp] {
    date {
      match => [ "timestamp", "ISO8601" ]
      target => "@timestamp"
    }
  }

  # Parse @timestamp from Filebeat
  if [@timestamp] {
    date {
      match => [ "@timestamp", "ISO8601" ]
      target => "@timestamp"
    }
  }

  # Add service identification for non-Docker logs
  if ![service_name] {
    mutate {
      add_field => { "service_name" => "turdparty" }
    }
  }
  
  # Process file injection events
  if [event_type] == "injection_created" {
    mutate {
      add_field => { "event_category" => "file_injection" }
      add_field => { "event_action" => "create" }
    }
  }
  
  if [event_type] == "injection_processed" {
    mutate {
      add_field => { "event_category" => "file_injection" }
      add_field => { "event_action" => "process" }
    }
  }
  
  if [event_type] == "injection_failed" {
    mutate {
      add_field => { "event_category" => "file_injection" }
      add_field => { "event_action" => "error" }
      add_field => { "log_level" => "ERROR" }
    }
  }
  
  # Process installation base events
  if [event_type] == "installation_base" {
    mutate {
      add_field => { "event_category" => "installation" }
      add_field => { "event_action" => "base_log" }
    }
    
    # Extract installation details
    if [installation_data] {
      ruby {
        code => "
          installation_data = event.get('installation_data')
          if installation_data.is_a?(Hash)
            installation_data.each do |key, value|
              event.set('installation_' + key, value)
            end
          end
        "
      }
    }
  }
  
  # Add geolocation if IP is present
  if [client_ip] {
    geoip {
      source => "client_ip"
      target => "geoip"
    }
  }
  
  # Parse user agent if present
  if [user_agent] {
    useragent {
      source => "user_agent"
      target => "ua"
    }
  }
  
  # Clean up fields
  mutate {
    remove_field => [ "host", "path", "tags" ]
  }
}

output {
  # Output to Elasticsearch with dynamic indexing
  elasticsearch {
    hosts => ["elasticsearch:9200"]

    # Dynamic index based on log source and service
    index => "turdparty-%{[service_name]:unknown}-%{+YYYY.MM.dd}"

    # Use different index for Docker logs
    if "docker_logs" in [tags] {
      index => "turdparty-docker-%{[service_name]:unknown}-%{+YYYY.MM.dd}"
    }

    template_name => "turdparty"
    template_pattern => "turdparty-*"
    template => {
      "index_patterns" => ["turdparty-*"],
      "settings" => {
        "number_of_shards" => 1,
        "number_of_replicas" => 0,
        "index.refresh_interval" => "5s"
      },
      "mappings" => {
        "properties" => {
          "@timestamp" => { "type" => "date" },
          "timestamp" => { "type" => "date" },
          "service" => { "type" => "keyword" },
          "service_name" => { "type" => "keyword" },
          "event_type" => { "type" => "keyword" },
          "event_category" => { "type" => "keyword" },
          "event_action" => { "type" => "keyword" },
          "injection_id" => { "type" => "keyword" },
          "filename" => { "type" => "text" },
          "target_path" => { "type" => "text" },
          "status" => { "type" => "keyword" },
          "level" => { "type" => "keyword" },
          "log_level" => { "type" => "keyword" },
          "message" => { "type" => "text" },
          "error_message" => { "type" => "text" },
          "details" => { "type" => "object" },
          "installation_data" => { "type" => "object" },
          "log_source" => { "type" => "keyword" },
          "platform" => { "type" => "keyword" },
          "container_id" => { "type" => "keyword" },
          "container_image" => { "type" => "keyword" },
          "container" => {
            "properties" => {
              "name" => { "type" => "keyword" },
              "id" => { "type" => "keyword" },
              "image" => {
                "properties" => {
                  "name" => { "type" => "keyword" },
                  "tag" => { "type" => "keyword" }
                }
              },
              "labels" => { "type" => "object" }
            }
          },
          "docker" => { "type" => "object" },
          "geoip" => {
            "properties" => {
              "location" => { "type" => "geo_point" },
              "country_name" => { "type" => "keyword" },
              "city_name" => { "type" => "keyword" }
            }
          }
        }
      }
    }
  }
  
  # Debug output to stdout (can be removed in production)
  stdout {
    codec => rubydebug
  }
}
